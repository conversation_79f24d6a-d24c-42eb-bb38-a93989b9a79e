import { requestClient } from '#/api/request';

// 获取所有客户
async function getAllCustomer(params: any) {
  return requestClient.get('/biz/customer/all', { params: { ...params } });
}

// 获取客户列表
async function getCustomerList(params: any) {
  return requestClient.get('/biz/customer/list', { params: { ...params } });
}

// 获取客户详情
async function getCustomerInfo(params: any) {
  return requestClient.get('/biz/customer/info', { params: { ...params } });
}

// 新增客户
async function addCustomer(data: any) {
  return requestClient.post('/biz/customer/add', data);
}

// 编辑客户
async function editCustomer(data: any) {
  return requestClient.post('/biz/customer/edit', data);
}

// 删除客户
async function deleteCustomer(data: any) {
  return requestClient.post('/biz/customer/delete', data);
}

export {
  getAllCustomer,
  getCustomerList,
  getCustomerInfo,
  addCustomer,
  editCustomer,
  deleteCustomer,
};
