import { requestClient } from '#/api/request';

// 获取所有分类
async function allType(params: any) {
  return requestClient.get('/prod/productType/all', { params: { ...params } });
}
// 新增分类`
async function createType(data: any) {
  return requestClient.post('/prod/productType/create', data);
}
// 修改分类`
async function updateType(data: any) {
  return requestClient.post('/prod/productType/update', data);
}
// 删除分类
async function deleteType(data: any) {
  return requestClient.post('/prod/productType/delete', data);
}
// 获取所有产品分页
/**
 * @description: Get product list
 *
 * */
async function getList(params: any) {
  return requestClient.get('/prod/product/list', { params: { ...params } });
}
// 获取所有产品
async function getAll(params: any) {
  return requestClient.get('/prod/product/all', { params: { ...params } });
}
// 获取单个产品信息
async function getInfo(params: any) {
  return requestClient.get('/prod/product/info', { params: { ...params } });
}
// 新增产品
async function createProduct(data: any) {
  return requestClient.post('/prod/product/create', data);
}
// 修改产品
async function updateProduct(data: any) {
  return requestClient.post('/prod/product/update', data);
}
// 删除产品
async function deleteProduct(data: any) {
  return requestClient.post('/prod/product/delete', data);
}

export {
  allType,
  getList,
  createType,
  updateType,
  deleteType,
  getAll,
  getInfo,
  createProduct,
  updateProduct,
  deleteProduct,
};
