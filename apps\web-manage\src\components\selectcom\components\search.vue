<script lang="ts" setup>
import { useVbenForm } from '#/adapter/form';

// =========================================================================
const props = defineProps({
  // 搜索参数
  schema: {
    type: Array<any>,
    default: () => [],
  },
});

// =========================================================================
const emits = defineEmits(['search']);
const onSubmit = (values: Record<string, any>) => {
  emits('search', values);
};
const onReset = (values: Record<string, any>) => {
  emits('search', values);
  formApi.resetForm();
};

// =========================================================================

const [QueryForm, formApi] = useVbenForm({
  compact: true,
  // 默认展开
  collapsed: false,
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelWidth: 0,
  },
  // 提交函数
  handleSubmit: onSubmit,
  handleReset: onReset,
  layout: 'horizontal',
  schema: props.schema,
  // 是否可展开
  showCollapseButton: false,
  submitButtonOptions: {
    content: '查询',
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
});
</script>

<template>
  <QueryForm />
</template>
