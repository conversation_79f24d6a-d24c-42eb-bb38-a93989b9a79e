import type { UploadFile } from 'ant-design-vue';
import { requestClient } from '#/api/request';
import OSS from 'ali-oss';
import dayjs from 'dayjs';

interface OSSConfig {
  accessKeyId: string;
  accessKeySecret: string;
  stsToken: string;
  region: string;
  bucket: string;
  expiration: string;
}

interface UploadToOSSParams {
  file: File;
  fileTypeTag: string;
  chunkSize?: number;
}

//生成随机字符串默认8位
function randomString(len = 8) {
  let $chars =
    'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'; /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
  let maxPos = $chars.length;
  let pwd = '';
  for (let i = 0; i < len; i++) {
    pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
  }
  return pwd;
}
/**
 * 更新file的name
 */
function updateFileName(fileName: string) {
  var startIndex = fileName.lastIndexOf('.');
  var suffix = fileName.substring(startIndex, fileName.length).toLowerCase();

  let date = dayjs().format('YYYYMMDD');
  //获取时间戳
  let time = dayjs().valueOf();
  return `${date}/${randomString()}${time}${suffix}`;
}

/**
 * 获取OSS配置信息
 */
async function getOSSConfig(): Promise<OSSConfig> {
  return requestClient.get<OSSConfig>('/sys/common/getStsToken');
}

/**
 * 上传文件到阿里云OSS
 * @param file 要上传的文件
 * @param fileTypeTag 文件类型标签
 * @param chunkSize 分片大小，默认5MB
 * @returns Promise<string> 返回文件URL
 */
export async function uploadToOSS({
  file,
  fileTypeTag,
  chunkSize = 5 * 1024 * 1024,
}: UploadToOSSParams): Promise<string> {
  let fileKey = updateFileName(file.name);
  console.log('文件信息:', file);
  
  // 获取OSS配置
  const ossConfig: any = await getOSSConfig();
  console.log('OSS配置:', ossConfig);
  
  // 创建OSS客户端
  const client = new OSS({
    region: ossConfig.region,
    accessKeyId: ossConfig.accessKeyId,
    accessKeySecret: ossConfig.accessKeySecret,
    bucket: ossConfig.bucket,
    stsToken: ossConfig.securityToken,
  });
  
  let ossKey = `${ossConfig.prefixPath}${fileKey}`;
  if (fileTypeTag && fileTypeTag != '') {
    ossKey = `${ossConfig.prefixPath}${fileTypeTag}/${fileKey}`;
  }
  
  // 生成OSS文件路径和URL
  const ossFilePath = ossKey;
  const fileUrl = `${ossConfig.protocol}://${ossConfig.host}/${ossKey}`;

  // 使用分片上传
  await client.multipartUpload(ossFilePath, file, {
    partSize: chunkSize,
    // 设置并发上传的分片数量。
    parallel: 8,
    headers: {
      'Cache-Control': 'public',
    },
  });
  
  console.log('上传成功，文件URL:', fileUrl);
  return fileUrl;
}

/**
 * 简化版自定义上传函数，仅返回 fileUrl
 */
export async function customOssUpload({
  file,
  fileTypeTag,
  chunkSize,
}: {
  file: File;
  fileTypeTag: string;
  chunkSize?: number;
}): Promise<string> {
  return uploadToOSS({ file, fileTypeTag, chunkSize });
}
