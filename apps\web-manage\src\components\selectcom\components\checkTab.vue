<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { customReq } from '#/api/config';

const props = defineProps({
  apiUrl: { type: String, default: '' },
  columns: { type: Array<any>, default: () => [] },
  keyField: { type: String, default: 'value' },
  checkRowKeys: { type: Array<any>, default: () => [] },
  labelFieldKey: { type: String, default: 'label' },
  defaultSearch: { type: Object, default: () => {} },
});
const emits = defineEmits(['checkSelect']);
const tableData: any = ref([]);

// =======================================================================================
const clearCheckboxRow = () => {
  gridApi.grid.clearCheckboxRow();
  gridApi.grid.clearCheckboxReserve();
};
const setCheckboxRow = (row: any, checked: boolean = true) => {
  gridApi.grid.setCheckboxRow(row, checked);
};

const searchParams: any = ref({});
const search = (e: any) => {
  searchParams.value = e;
  gridApi.query();
};

defineExpose({
  clearCheckboxRow,
  setCheckboxRow,
  search,
});

// =======================================================================================
const gridOptions: VxeGridProps<any> = {
  columns: props.columns,
  rowConfig: {
    keyField: props.keyField,
  },
  columnConfig: {
    resizable: false,
  },
  checkboxConfig: {
    // highlight: true,
    labelField: props.labelFieldKey,
    checkRowKeys: props.checkRowKeys,
    reserve: true,
  },
  exportConfig: {},
  height: '500',
  keepSource: true,
  align: 'left',
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        const getParams: any = {
          pageCurrent: page.currentPage,
          pageSize: page.pageSize,
          ...props.defaultSearch,
          ...searchParams.value,
        };

        const resData = await customReq(props.apiUrl, getParams);
        tableData.value = resData.dataItems;
        return {
          items: resData.dataItems,
          total: resData.recordCount,
        };
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    // import: true,
    // refresh: { code: 'query' },
    zoom: false,
  },
};

const gridEvents: any = {
  // 全选(单选)
  checkboxChange: ({ row }: any) => {
    emits('checkSelect', { type: 'check', val: row });
  },

  // 全选
  checkboxAll: ({ checked }: any) => {
    emits('checkSelect', {
      type: 'checkAll',
      val: tableData.value,
      isAll: checked,
    });
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});
</script>

<template>
  <Grid />
</template>
