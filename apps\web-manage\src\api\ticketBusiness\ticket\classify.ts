import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getTicketTypeList(params: Recordable<any>) {
  return requestClient.get('/tkt/ticketType/list', { params });
}

async function getTicketTypeAllList(params: Recordable<any>) {
  return requestClient.get('/tkt/ticketType/all', { params });
}

async function getTicketTypeInfo(id: string) {
  return requestClient.get('/tkt/ticketType/info', { params: { id } });
}

async function createTicketType(data: Recordable<any>) {
  return requestClient.post('/tkt/ticketType/create', data);
}

async function updateTicketType(data: Recordable<any>) {
  return requestClient.post('/tkt/ticketType/update', data);
}

async function deleteTicketType(id: string) {
  return requestClient.post('/tkt/ticketType/delete', { id });
}

export {
  getTicketTypeList,
  getTicketTypeAllList,
  getTicketTypeInfo,
  createTicketType,
  updateTicketType,
  deleteTicketType,
};
