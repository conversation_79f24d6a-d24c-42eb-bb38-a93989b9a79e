<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { ref, watch } from 'vue';
import { Button, Table } from 'ant-design-vue';
import { useVbenModal } from '@vben/common-ui';
import CouponList from './couponList.vue';

const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(['update:value']);
const couponList = ref<any[]>([]);
watch(
  () => props.value,
  (newVal) => {
    couponList.value = newVal;
  },
  {
    immediate: true,
    deep: true,
  },
);

const columns = ref<any>([
  {
    title: '优惠券名称',
    dataIndex: 'couponName',
    align: 'center',
  },
  {
    title: '优惠方式',
    dataIndex: 'couponType',
    align: 'center',
  },
  {
    title: '优惠金额/折扣',
    dataIndex: 'couponValue',
    align: 'center',
  },
  {
    title: '有效期',
    dataIndex: 'validType',
    align: 'center',
  },
  {
    title: '使用门槛',
    dataIndex: 'thresholdType',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 120,
  },
]);

// 门票列表弹窗
const [UserModel, userModelApi] = useVbenModal({
  connectedComponent: CouponList,
  destroyOnClose: true,
});

const listChange = (data: any) => {
  // 如果data中存在UserList中的数据，则替换
  let list = [...data];
  couponList.value = list;
  emit('update:value', couponList.value);
};

const addUser = () => {
  userModelApi.setData({}).open();
};
const delUser = (val: any) => {
  let data = {
    ...val,
  };
  couponList.value = changeData(couponList.value).filter(
    (item: any) => item.id !== data.id,
  );
  emit('update:value', couponList.value);
};
const changeData = (val: any) => {
  let list = [...val];
  return list;
};
</script>
<template>
  <div class="w-full">
    <Button class="mb-2" type="primary" @click="addUser">选择优惠券</Button>
    <Table
      :columns="columns"
      :dataSource="couponList"
      :pagination="false"
      bordered
      rowClassName="custom-row"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'couponType'">
          {{ record.couponType == 1 ? '满减券' : '折扣券' }}
        </template>
        <template v-if="column.dataIndex === 'validType'">
          {{
            record.validType == 1
              ? '永久有效'
              : record.validType == 2
                ? `${record.validBeginDate}~${record.validEndDate}`
                : `领取后${record.validDayNum}天内有效`
          }}
        </template>
        <template v-if="column.dataIndex === 'thresholdType'">
          {{
            record.thresholdType == 1
              ? '无门槛'
              : `最低消费${record.thresholdPrice}元`
          }}
        </template>
        <template v-if="column.dataIndex === 'action'">
          <Button type="link" danger @click="delUser(record)">删除</Button>
        </template>
      </template>
    </Table>
    <UserModel :selectedCoupons="changeData(couponList)" @change="listChange">
    </UserModel>
  </div>
</template>
<style>
.custom-row .ant-table-cell {
  padding: 6px 16px !important;
}
</style>
