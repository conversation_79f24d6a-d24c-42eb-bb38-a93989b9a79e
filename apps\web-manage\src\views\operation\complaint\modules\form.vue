<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Descriptions, DescriptionsItem, Image, message, Select, Textarea } from 'ant-design-vue';
import { useAccessStore } from '@vben/stores';
import { complaintDeal } from '#/api'
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

const emits = defineEmits(['success']);
const complaintInfo = ref<any>({});
const type = ref('edit');
const submitParams = ref<any>({});
const [Model, modelApi] = useVbenModal({
    title: '投诉建议',
    async onConfirm() {
        await handleSubmit();
    },
    onOpenChange(isOpen) {
        let data = modelApi.getData();
        if (isOpen) {
            complaintInfo.value = data;
            type.value = data.type
            if (data.feedback || data.complaintStatus) {
                submitParams.value.feedback = data.feedback;
                submitParams.value.complaintStatus = data.complaintStatus;
            }
            if (data.type == 'info') {
                modelApi.setState({
                    showCancelButton: false,
                    confirmText: '关闭',
                })
            }
        }
    },
});

const handleSubmit = async () => {
    if (type.value == 'info') {
        modelApi.close();
    } else {
        await complaintDeal({ ...submitParams.value, id: complaintInfo.value.id });
        message.success('处理成功');
        modelApi.close();
        emits('success');
    }
};

const filterType = (val: any) => {
    return accessAllEnums.value?.complaintType.list.find(
        (item: any) => item.value === val,
    )?.label;
};

const customLabelStyle = ref({
    width: '200px',
})
</script>
<template>
    <Model class="w-[50%]">
        <Descriptions title="" bordered :column="2" :label-style="customLabelStyle">
            <DescriptionsItem label="投诉类型">{{ filterType(complaintInfo.complaintType) }}</DescriptionsItem>
            <DescriptionsItem label="所属景区">{{ complaintInfo.scenicInfo.scenicName }}</DescriptionsItem>
            <DescriptionsItem label="投诉时间" :span="2">{{
                complaintInfo.createdAt
            }}</DescriptionsItem>
            <DescriptionsItem label="投诉内容" :span="2">{{ complaintInfo.complaintContent }}</DescriptionsItem>
            <DescriptionsItem label="投诉图片" :span="2">
                <div class="flex gap-2">
                    <template v-for="item in complaintInfo.complaintImg.split(',')">
                        <Image v-if="complaintInfo.complaintImg" :src="item"
                            style="width: 60px; height: 60px;object-fit: cover;" />
                    </template>
                </div>
            </DescriptionsItem>
            <DescriptionsItem label="联系人">{{ complaintInfo.linkperson }}</DescriptionsItem>
            <DescriptionsItem label="联系电话">{{
                complaintInfo.linktel
            }}</DescriptionsItem>
            <DescriptionsItem label="办结状态" :span="2">
                <div v-if="type == 'info'">
                    {{
                        complaintInfo.complaintStatus == 1 ? '未办结' : '已办结'
                    }}
                </div>
                <Select v-model:value="submitParams.complaintStatus" :options="[
                    { label: '未办结', value: 1 },
                    { label: '已办结', value: 2 },
                ]" placeholder="请选择办结状态" class="w-[200px]" v-else></Select>
            </DescriptionsItem>
            <DescriptionsItem label="反馈内容" :span="2">
                <div v-if="type == 'info'">
                    {{ complaintInfo.feedback }}
                </div>
                <Textarea v-model:value="submitParams.feedback" placeholder="请输入反馈内容" v-else></Textarea>
            </DescriptionsItem>
            <DescriptionsItem label="办结人" :span="2" v-if="complaintInfo.complaintStatus == 2">
                {{ complaintInfo.handlerInfo?.name }}（{{ complaintInfo.handlerInfo?.phone }}）
            </DescriptionsItem>
        </Descriptions>
    </Model>
</template>
