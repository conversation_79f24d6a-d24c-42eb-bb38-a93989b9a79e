<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { ref, watch } from 'vue';
import {
  Button,
  Table,
  Tag
} from 'ant-design-vue';
import { useVbenModal } from '@vben/common-ui';
import UserList from './userList.vue';

const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(['update:value']);
const userList = ref<any[]>([]);
watch(
  () => props.value,
  (newVal) => {
    userList.value = newVal;
  },
  {
    immediate: true,
    deep: true,
  },
);

const columns = ref<any>([
  {
    title: '账户名',
    dataIndex: 'userName',
    align: 'center',
  },
  {
    title: '姓名',
    dataIndex: 'name',
    align: 'center',
  },
  {
    title: '性别',
    dataIndex: 'sex',
    align: 'center',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    align: 'center',
  },
  {
    title: '状态',
    dataIndex: 'status',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 120,
  },
]);

// 门票列表弹窗
const [UserModel, userModelApi] = useVbenModal({
  connectedComponent: UserList,
  destroyOnClose: true,
});

const listChange = (data: any) => {
  // 如果data中存在UserList中的数据，则替换
  let list = [...data]
  userList.value = list
  emit('update:value', userList.value);
};

const addUser = () => {
  userModelApi.setData({}).open();
};
const delUser = (val: any) => {
  let data = {
    ...val
  };
  userList.value = changeData(userList.value).filter(
    (item: any) => item.id !== data.id,
  );
  emit('update:value', userList.value);
};
const changeData = (val: any) => {
  let list = [...val]
  return list;
};

const filterStatus = (val: any) => {
  const statusMap = [
    { color: 'success', label: '启用', value: 1 },
    { color: 'error', label: '禁用', value: -1 },
    { color: 'warning', label: '锁定', value: -2 },
  ]
  return statusMap.find((item: any) => item.value === val);
};
</script>
<template>
  <div class="w-full">
    <Button class="mb-2" type="primary" @click="addUser">选择售票员</Button>
    <Table :columns="columns" :dataSource="userList" :pagination="false" bordered rowClassName="custom-row"
      v-if="userList.length > 0">
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'sex'">
          {{ record.sex === 1 ? '男' : record.sex === 2 ? '女' : '保密' }}
        </template>
        <template v-if="column.dataIndex === 'status'">
          <Tag :color="filterStatus(record.status)?.color">{{ filterStatus(record.status)?.label }}</Tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <Button type="link" danger @click="delUser(record)">删除</Button>
        </template>
      </template>
    </Table>
    <UserModel :selectedUsers="changeData(userList)" @change="listChange">
    </UserModel>
  </div>
</template>
