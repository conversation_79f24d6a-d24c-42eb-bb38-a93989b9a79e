<script setup lang="ts">
import { ref, computed } from 'vue';
import { Select, SelectOption, Calendar } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
  placeholder: {
    type: String,
    default: '请选择日期',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:value']);
// 状态
const selectedDates = ref<any[]>([]);
const outerSelectOpen = ref(false); // 手动控制下拉菜单状态

// 计算属性：输入框显示的值
const inputValue = computed(() => {
  if (selectedDates.value.length === 0) return [];
  return selectedDates.value.map((date) => formatDate(date));
});

// 选择日期的处理函数
const onSelectDate = (date: any, info: any) => {
  if (info.source !== 'date') return;

  const formattedDate = date.format('YYYY-MM-DD');
  if (!selectedDates.value.some((d) => d === formattedDate)) {
    selectedDates.value.push(formattedDate);
  }

  // 保持下拉菜单打开
  outerSelectOpen.value = true;
  emit('update:value', selectedDates.value);
};

// 移除日期
const deselect = (val: any) => {
  selectedDates.value = selectedDates.value.filter((d) => d !== val);
  outerSelectOpen.value = true; // 保持下拉菜单打开
  emit('update:value', selectedDates.value);
};

// 格式化日期显示
const formatDate = (dateString: any) => {
  return dateString;
};

const yearSelect = ref<any>(null);
const monthSelect = ref<any>(null);

const onPanelChange = (value: any, mode: string) => {
  console.log(value, mode);
};

const getMonths = (value: any) => {
  const localeData = value.localeData();
  const months = [];
  for (let i = 0; i < 12; i++) {
    months.push(localeData.monthsShort(value.month(i)));
  }
  return months;
};

const getYears = (value: any) => {
  const year = value.year();
  const years = [];
  for (let i = year - 10; i < year + 10; i += 1) {
    years.push(i);
  }
  return years;
};

// 处理外部下拉菜单状态变化
const handleOuterDropdownChange = (open: boolean) => {
  outerSelectOpen.value = open;
};
</script>

<template>
  <div class="date-picker-container">
    <Select
      v-model:value="inputValue"
      :placeholder="placeholder"
      class="w-full"
      :allow-clear="true"
      mode="multiple"
      :disabled="disabled"
      @deselect="deselect"
      popupClassName="!min-w-[300px] !w-[300px] custom-dropdown"
      :open="outerSelectOpen"
      @dropdownVisibleChange="handleOuterDropdownChange"
    >
      <!-- 修改1：完全移除 menuNode 渲染 -->
      <template #dropdownRender>
        <!-- 关键修改2：阻止事件冒泡的容器 -->
        <div
          class="custom-dropdown-content"
          @mousedown="
            (e) => {
              e.preventDefault();
              e.stopPropagation();
            }
          "
        >
          <!-- 修改4：直接渲染日历组件 -->
          <Calendar
            :fullscreen="false"
            @select="onSelectDate"
            :default-value="dayjs()"
            @panelChange="onPanelChange"
          >
            <template
              #headerRender="{ value: current, type, onChange, onTypeChange }"
            >
              <div class="pb-1 text-right">
                <Select
                  ref="yearSelect"
                  size="small"
                  :dropdown-match-select-width="false"
                  class="my-year-select"
                  :value="String(current.year())"
                  @change="
                    (newYear: any) => {
                      onChange(current.year(+newYear));
                    }
                  "
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                >
                  <SelectOption
                    v-for="val in getYears(current)"
                    :key="String(val)"
                    class="year-item"
                  >
                    {{ val }}
                  </SelectOption>
                </Select>
                <Select
                  ref="monthSelect"
                  size="small"
                  class="ml-2"
                  :dropdown-match-select-width="false"
                  :value="String(current.month())"
                  @change="
                    (selectedMonth: any) => {
                      onChange(
                        current.month(parseInt(String(selectedMonth), 10)),
                      );
                    }
                  "
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                >
                  <SelectOption
                    v-for="(val, index) in getMonths(current)"
                    :key="String(index)"
                    class="month-item"
                  >
                    {{ val }}
                  </SelectOption>
                </Select>
              </div>
            </template>
          </Calendar>
        </div>
      </template>
    </Select>
  </div>
</template>

<style lang="scss" scoped>
.date-picker-container {
  width: 100%;
}

// 自定义下拉内容容器
.custom-dropdown-content {
  padding: 8px;
}

// 确保自定义内容可见
:deep(.custom-dropdown) {
  overflow: visible !important;

  .ant-select-dropdown {
    overflow: visible !important;
  }

  // 隐藏原始选项列表
  .ant-select-item {
    display: none;
  }

  // 隐藏空状态
  .ant-empty {
    display: none;
  }
}

// 调整内部下拉菜单层级
:deep(.ant-select-dropdown) {
  z-index: 1051 !important;
}
</style>

<style>
/* 自定义Popover样式，限制大小为400*400像素 */
.custom-popover .ant-popover-inner-content {
  width: 400px;
  height: 400px;
  overflow: auto;
}

.custom-popover .ant-calendar-picker {
  width: 100%;
  height: 100%;
}
</style>
