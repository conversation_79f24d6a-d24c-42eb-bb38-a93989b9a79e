import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getSpotList(params: Recordable<any>) {
  return requestClient.get('/op/spot/list', { params });
}
async function getAllSpotList(params: Recordable<any>) {
  return requestClient.get('/op/spot/all', { params });
}
async function getSpotInfo(id: string) {
  return requestClient.get('/op/spot/info', { params: { id } });
}

async function createSpot(data: Recordable<any>) {
  return requestClient.post('/op/spot/create', data);
}

async function updateSpot(data: Recordable<any>) {
  return requestClient.post('/op/spot/update', data);
}

async function deleteSpot(id: string) {
  return requestClient.post('/op/spot/delete', { id });
}

async function changeSpotStatus(data: Recordable<any>) {
  return requestClient.post('/op/spot/changeStatus', data);
}

export {
  getSpotList,
  getAllSpotList,
  getSpotInfo,
  createSpot,
  updateSpot,
  deleteSpot,
  changeSpotStatus,
};
