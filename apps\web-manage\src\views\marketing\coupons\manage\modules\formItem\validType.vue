<script lang="ts" setup>
import { Select, RangePicker, InputNumber } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';
const emit = defineEmits(['blur', 'change']);
const modelValue = defineModel<[number, [Dayjs, Dayjs] | number]>({
    default: () => [1, null],
});

function onChange() {
    emit('change', modelValue.value);
}
</script>
<template>
    <div class="flex h-[32px] w-full items-center gap-1">
        <div class="w-2/5">
            <Select v-model:value="modelValue[0]" :options="[
                { label: '永久有效', value: 1 },
                { label: '固定日期', value: 2 },
                { label: '领取后x天内有效', value: 3 },
            ]" @change="onChange" @blur="emit('blur', modelValue)" class="w-full" />
        </div>
        <div class="flex flex-1 items-center" v-if="modelValue[0] === 2">
            <div class="mr-2 w-[120px] text-right text-sm font-[500] leading-6">
                有效期日期
            </div>
            <RangePicker v-model:value="modelValue[1]" format="YYYY-MM-DD" :placeholder="['开始日期', '结束日期']" separator="至"
                valueFormat="YYYY-MM-DD" allow-clear @change="onChange" @blur="emit('blur', modelValue)"
                class="w-full" />
        </div>
        <div class="flex flex-1 items-center" v-if="modelValue[0] === 3">
            <div class="mr-2 w-[120px] text-right text-sm font-[500] leading-6">
                有效天数
            </div>
            <InputNumber v-model:value="modelValue[1]" placeholder="请输入有效天数" allow-clear @change="onChange"
                @blur="emit('blur', modelValue)" class="w-full" />
            <div class="ml-2 text-sm font-[500] leading-6">天</div>
        </div>
    </div>
</template>
