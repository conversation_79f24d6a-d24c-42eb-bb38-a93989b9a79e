<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Table } from 'ant-design-vue';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
import { tradeLog } from '#/api';
const { accessAllEnums } = toRefs(useAccessStore());

const params = ref({
  page: 1,
  pageSize: 10,
  cardId: undefined,
});

const logList = ref<any>([]);
const total = ref(0);
const columns = ref<any>([
  {
    title: '姓名',
    dataIndex: 'name',
    align: 'center',
  },
  {
    title: '交易类型',
    dataIndex: 'tradeType',
    align: 'center',
  },
  {
    title: '交易前余额',
    dataIndex: 'beforeAmount',
    align: 'center',
  },
  {
    title: '交易金额',
    dataIndex: 'amount',
    align: 'center',
  },
  {
    title: '交易后余额',
    dataIndex: 'afterAmount',
    align: 'center',
  },
  {
    title: '消费类型',
    dataIndex: 'consumeType',
    align: 'center',
  },
  {
    title: '消费订单',
    dataIndex: 'orderNo',
    align: 'center',
  },
  {
    title: '交易时间',
    dataIndex: 'createdAt',
    align: 'center',
  },
]);
const [Model, modelApi] = useVbenModal({
  title: '余额明细',
  showCancelButton: false,
  confirmText: '关闭',
  async onConfirm() {
    modelApi.close();
  },
  onOpenChange(isOpen) {
    let data = modelApi.getData();
    if (isOpen) {
      params.value.cardId = data.cardId;
      getLogList();
    }
  },
});

const getLogList = async () => {
  let res = await tradeLog(params.value);
  logList.value = res.list;
  total.value = res.total;
};

const filterTradeType = (array: any[], val: any) => {
  return array.find((item: any) => item.value === val)?.label;
};
</script>
<template>
  <Model class="w-[50%]">
    <Table :columns="columns" :dataSource="logList" rowKey="id" :pagination="{
      current: params.page,
      pageSize: params.pageSize,
      total: total,
      onChange: (page, pageSize) => {
        params.page = page;
        params.pageSize = pageSize;
        getLogList();
      },
      showTotal: (total) => `共 ${total} 条`,
    }" bordered>
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'amount'">
          {{ record.amount }}
          <p v-if="Number(record.giftAmount) > 0">（赠：{{ record.giftAmount }}）</p>
        </template>
        <template v-if="column.dataIndex === 'tradeType'">
          {{
            filterTradeType(
              accessAllEnums.prepaidCardTradeType.list,
              record.tradeType,
            )
          }}
        </template>
        <template v-if="column.dataIndex === 'consumeType'">
          {{
            filterTradeType(
              accessAllEnums.prepaidCardConsumeType.list,
              record.consumeType,
            )
          }}
        </template>
        <template v-if="column.dataIndex === 'name'">
          {{ record.cardInfo.name }}
        </template>
      </template>
    </Table>
  </Model>
</template>
