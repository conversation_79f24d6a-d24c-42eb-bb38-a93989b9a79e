<script setup lang="ts">
import { Page, useVbenModal } from '@vben/common-ui';
import {
  Button,
  Form,
  Input,
  Select,
  Pagination,
  Tag,
  Modal,
  Empty,
} from 'ant-design-vue';
import { ref, toRefs, onMounted } from 'vue';
import { useAccessStore } from '@vben/stores';
import { getCouponList, getCouponInfo, revokeCoupon, changeCouponStatus } from '#/api';
const { accessAllEnums } = toRefs(useAccessStore());

import AddForm from './modules/addForm.vue';
import EditForm from './modules/editForm.vue';
import SendForm from './modules/sendForm.vue';
import Info from './modules/info.vue';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const [AddFormModel, addFormModelApi] = useVbenModal({
  connectedComponent: AddForm,
  destroyOnClose: true,
});
const [EditFormModel, editFormModelApi] = useVbenModal({
  connectedComponent: EditForm,
  destroyOnClose: true,
});
const [SendFormModel, sendFormModelApi] = useVbenModal({
  connectedComponent: SendForm,
  destroyOnClose: true,
});
const [InfoModel, infoModelApi] = useVbenModal({
  connectedComponent: Info,
  destroyOnClose: true,
});
const sizeOptions = ref<any>([
  { label: '10条/页', value: 10 },
  { label: '20条/页', value: 20 },
  { label: '50条/页', value: 50 },
  { label: '100条/页', value: 100 },
]);
const searchParams: any = ref({
  page: 1,
  pageSize: 10,
  name: null,
  couponType: null,
  status: null,
});
const couponList: any = ref([]);
const total = ref(0);
onMounted(() => {
  getList();
});
const getList = async () => {
  const res = await getCouponList({
    ...searchParams.value,
  });
  console.log(res);
  couponList.value = res.list;
  total.value = res.total;
};

// 页码切换
function currentChange(page: number) {
  console.log(page);
  searchParams.value.page = page;
  getList();
}
// 大小切换
function sizeChange(val: any) {
  console.log(val);
  searchParams.value.pageSize = val;
  getList();
}

// 操作
// 查询
const handleReset = async () => {
  searchParams.value = {
    page: 1,
    pageSize: 10,
    name: null,
    couponType: null,
    status: null,
  };
  getList();
};
const handleSearch = async () => {
  searchParams.value.page = 1;
  getList();
};
// 新增
const handleAdd = async () => {
  addFormModelApi.open();
};

// 发放
const handleSend = async () => {
  sendFormModelApi.open();
};
// 编辑
const handleEdit = async (row: any) => {
  const res = await getCouponInfo({ id: row.id });
  editFormModelApi.setData(res).open();
};
// 详情
const handleInfo = async (row: any) => {
  const res = await getCouponInfo({ id: row.id });
  infoModelApi.setData(res).open();
};
// 上下架
const onStatusChange = async (row: any) => {
  try {
    await confirm(
      (row.status === 1 ? '下架后，此券无法领取，已领取的不受影响，' : '') +
      '确认' +
      (row.status === 1 ? '下架' : '上架') +
      '优惠券【' +
      row.couponName +
      '】吗？',
      (row.status === 1 ? '下架' : '上架') + '优惠券',
    );
    await changeCouponStatus({
      id: row.id,
      status: row.status === 1 ? 3 : 1,
    });
    getList();
  } catch (error: any) {
    console.log(error);
  }
};
// 作废
const onRevoke = async (row: any) => {
  try {
    await confirm('作废后，优惠券将下架，同时已领取的将无法使用，确认作废优惠券【' + row.couponName + '】吗？', '作废优惠券');
    await revokeCoupon(row.id);
    getList();
  } catch (error) {
    console.log(error);
  }
};

/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
const confirm = (content: string, title: string) => {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
};
const filterStatus = (val: any) => {
  return accessAllEnums.value?.couponStatus.list.find(
    (item: any) => item.value === val,
  )?.label;
};
</script>
<template>
  <Page auto-content-height>
    <div class="bg-color-white rounded-md p-2 flex flex-col" style="height: calc(100vh - 120px);">
      <div class="relative flex flex-col h-full">
        <div class="relative rounded py-3 pb-8 flex-shrink-0">
          <div>
            <Form layout="inline" :model="searchParams" class="w-full">
              <div class="grid w-full grid-cols-4 gap-2 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5">
                <div class="w-full pb-2">
                  <Input v-model:value="searchParams.name" placeholder="请输入优惠券名称" class="w-full" />
                </div>
                <div class="w-full pb-2">
                  <Select v-model:value="searchParams.couponType" placeholder="请选择优惠券类型" :options="[
                    {
                      label: '满减劵',
                      value: 1,
                    },
                    {
                      label: '折扣券',
                      value: 2,
                    },
                  ]" class="w-full" />
                </div>
                <div class="w-full pb-2">
                  <Select v-model:value="searchParams.status" placeholder="请选择状态"
                    :options="accessAllEnums.couponStatus.list" class="w-full" />
                </div>
                <div class="w-full pb-2"></div>
                <div class="w-full pb-2 text-right">
                  <Button @click="handleReset">重置</Button>
                  <Button type="primary" class="ml-2" @click="handleSearch">搜索</Button>
                </div>
              </div>
            </Form>
            <div
              class="bg-background-deep z-100 absolute -left-2 bottom-1 h-2 w-[calc(100%+1rem)] overflow-hidden md:bottom-2 md:h-3">
            </div>
          </div>
        </div>
        <!-- 优惠券内容 -->
        <div class="flex flex-col flex-1 min-h-0">
          <!-- 操作按钮 - 固定在上方 -->
          <div class="flex-shrink-0 gap-2 flex py-2 h-[50px]">
            <Button type="primary" @click="handleAdd">新增优惠券</Button>
            <Button type="primary" @click="handleSend">发放优惠券</Button>
          </div>

          <!-- 优惠券列表 - 可滚动区域 -->
          <div class="flex-1 overflow-y-auto mt-2 mb-2 min-h-0">
            <div class="grid grid-cols-4 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 pr-2"
              v-if="couponList && couponList.length">
              <div v-for="(item, index) in couponList" :key="index" class="relative overflow-hidden rounded-lg shadow"
                style="background: #f5f6f9">
                <div class=" relative rounded-lg py-2" :class="item.status > 1 ? 'grayscale-[1]' : ''"
                  style="background: #fff; border: 1px solid #eff1f7">
                  <div class="absolute left-0 w-1" :class="`bg-color-${item.couponType}`"
                    style="top: 10%; height: 80%; border-radius: 0 4px 4px 0"></div>
                  <div class="items-center justify-center pl-4 pb-2" style="width: 100%">
                    <div>
                      <div>
                        <Tag class="mr-2" :color="item.couponType === 1 ? 'warning' : 'error'">{{ item.couponType === 1 ? '满减券' :
                          '折扣券'
                          }}</Tag>
                        <span v-if="item.couponType === 1">{{ '￥' }}</span>
                        <span class="px-1 text-4xl font-medium">
                          {{ item.couponValue }}
                        </span>
                        <span v-if="item.couponType === 2">折</span>

                      </div>
                    </div>
                  </div>
                  <div class=" overflow-hidden px-4 pt-2" style="border-top: 1px solid #eff1f7">
                    <div class="truncate text-lg font-medium">
                      {{ item.couponName }}
                    </div>
                    <div class="mt-2">
                      <div>
                        <span class="opacity-50">有效期限:</span>
                        <span class="ml-1" v-if="item.validType == 1">永久有效</span>
                        <span class="ml-1" v-if="item.validType == 2">{{
                          item.validBeginDate + '~' + item.validEndDate
                        }}</span>
                        <span class="ml-1" v-if="item.validType == 3">领取后{{ item.validDayNum }}天内有效</span>
                      </div>
                      <div class="mt-2">
                        <span class="opacity-50">使用门槛:</span>
                        <span class="ml-1">{{
                          item.thresholdType == 1
                            ? '无门槛'
                            : '最低消费' + item.thresholdPrice + '元'
                        }}</span>
                      </div>
                    </div>
                    <div class="cornerMark" v-if="item.status !== 1">
                      {{ filterStatus(item.status) }}
                    </div>
                    <div class="mt-2 flex">
                      <div>
                        <span class="opacity-50">总数量:</span>
                        <span class="ml-1">{{ item.stockNum }}</span>
                      </div>
                      <div class="ml-3">
                        <span class="opacity-50">剩余数量:</span>
                        <span class="ml-1">{{ item.availableStockNum }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="flex justify-end px-4 py-1.5">
                  <Button type="link" size="small" @click="handleEdit(item)">
                    编辑
                  </Button>
                  <Button type="link" size="small" @click="handleInfo(item)"> 详情 </Button>
                  <Button type="link" size="small" danger v-if="item.status == 1" @click="onStatusChange(item)">
                    下架
                  </Button>
                  <Button type="link" size="small" v-if="item.status == 3 || item.status == 0"
                    @click="onStatusChange(item)">
                    上架
                  </Button>
                  <Button type="link" size="small" v-if="item.status !== 0 && item.status !== 4" danger
                    @click="onRevoke(item)">
                    作废
                  </Button>
                </div>
              </div>
            </div>
            <div class="pt-20" v-else>
              <Empty :image="simpleImage" description="暂无优惠券" />
            </div>
          </div>

          <!-- 分页操作 - 固定在下方 -->
          <div class="flex-shrink-0 flex justify-between items-center h-[40px]">
            <div>
              <span v-if="total != null" class="mr-3 text-sm">共<span class="text-gray-500">{{ total }}</span>条</span>
              <Select v-model:value="searchParams.pageSize" size="small" :options="sizeOptions" @change="sizeChange"
                style="width: 130px">
              </Select>
            </div>

            <div>
              <Pagination :total="total" size="small" v-model:page-size="searchParams.pageSize" show-less-items
                @change="currentChange" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <AddFormModel @success="handleReset"></AddFormModel>
    <EditFormModel @success="handleReset"></EditFormModel>
    <SendFormModel @success="handleReset"></SendFormModel>
    <InfoModel></InfoModel>
  </Page>
</template>
<style lang="scss" scoped>
.bg-color-white {
  background-color: hsl(var(--card));
}

.bg-color-1 {
  background-color: hsl(var(--warning));
}

.bg-color-2 {
  background-color: hsl(var(--destructive));
}

.cornerMark {
  position: absolute;
  top: 10px;
  right: -20px;
  padding: 3px 25px;
  font-size: 12px;
  color: #fff;
  background: #bfbfbf;
  transform: rotate(45deg);
}
</style>
