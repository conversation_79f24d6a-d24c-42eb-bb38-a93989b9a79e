<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Descriptions, DescriptionsItem, Image, Badge } from 'ant-design-vue';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

const userInfo = ref<any>({});
const [Model, modelApi] = useVbenModal({
  title: '用户详情',
  showCancelButton: false,
  confirmText: '关闭',
  async onConfirm() {
    modelApi.close();
  },
  onOpenChange(isOpen) {
    let data = modelApi.getData();
    if (isOpen) {
      userInfo.value = data;
    }
  },
});

const filterStatus = (val: any) => {
  return accessAllEnums.value?.ticketCardStatus.list.find(
    (item: any) => item.value === val,
  )?.label;
};
</script>
<template>
  <Model class="w-[50%]">
    <Descriptions title="" bordered :column="2">
      <DescriptionsItem label="头像" :span="2">
        <Image
          v-if="userInfo.avatar"
          :src="userInfo.avatar"
          style="width: 60px; height: 60px; border-radius: 50%"
        />
      </DescriptionsItem>
      <DescriptionsItem label="昵称">{{ userInfo.nickname }}</DescriptionsItem>
      <DescriptionsItem label="姓名">{{ userInfo.name }}</DescriptionsItem>
      <DescriptionsItem label="手机号">{{ userInfo.phone }}</DescriptionsItem>
      <DescriptionsItem label="身份证">{{ userInfo.idcard }}</DescriptionsItem>
      <DescriptionsItem label="生日">{{ userInfo.birthday }}</DescriptionsItem>
      <DescriptionsItem label="性别">{{
        userInfo.sex === 1 ? '男' : '女'
      }}</DescriptionsItem>
      <DescriptionsItem label="地区" :span="2">{{
        userInfo.region
      }}</DescriptionsItem>
      <DescriptionsItem label="注册时间">{{
        userInfo.registerTime
      }}</DescriptionsItem>
      <DescriptionsItem label="最后登录时间">{{
        userInfo.lastLoginTime
      }}</DescriptionsItem>
      <DescriptionsItem label="最后登录IP">{{
        userInfo.lastLoginIp
      }}</DescriptionsItem>
      <DescriptionsItem label="状态"
        ><Badge
          :status="userInfo.status === 1 ? 'success' : 'error'"
          :text="userInfo.status === 1 ? '正常' : '黑名单'"
        />
      </DescriptionsItem>
    </Descriptions>
  </Model>
</template>
