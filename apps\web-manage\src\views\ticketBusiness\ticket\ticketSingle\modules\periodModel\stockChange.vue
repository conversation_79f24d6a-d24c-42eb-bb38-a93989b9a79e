<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { computed, ref, h, toRefs, watch } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import dayjs from 'dayjs';
import {
  Form,
  FormItem,
  InputNumber,
  Button,
  Table,
  Switch,
  DatePicker,
  message,
} from 'ant-design-vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import { getTicketPeriodInfo, updateTicketPeriod } from '#/api';
import TimeChange from './timeChange.vue';

const [ModelTime, modelTimeApi] = useVbenModal({
  connectedComponent: TimeChange,
  destroyOnClose: true,
});

const [ModelStock, modelStockApi] = useVbenModal({
  title: '库存设置',
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    let data = modelStockApi.getData<any>();
    dateRange.value = {
      purchaseBeginDate: data.purchaseBeginDate,
      purchaseEndDate: data.purchaseEndDate,
    };
    if (isOpen) {
      stockData.value.playDate = dayjs(data.playDate);
      stockData.value.ticketId = data.ticketId;
      stockData.value.periodList = data.periodList.map((item: any) => {
        return {
          ...item,
          time: `${item.beginTime} - ${item.endTime}`,
          isCustomStock: false,
          isCustomPrice: false,
        };
      });
      console.log(stockData.value, 'data');
    }
  },
});

const formData = ref<Recordable<any>>({});
const stockData = ref<Recordable<any>>({});
const dateRange = ref<any>({});
const columns = ref<any[]>([
  {
    title: '时段',
    dataIndex: 'time',
    width: 200,
    align: 'center',
  },
  {
    title: '自定义库存',
    dataIndex: 'isCustomStock',
    width: 120,
    align: 'center',
  },
  {
    title: '库存',
    dataIndex: 'periodStock',
    align: 'center',
    width: 230,
  },
  {
    title: '自定义价格',
    dataIndex: 'isCustomPrice',
    width: 120,
    align: 'center',
  },
  {
    title: '价格',
    dataIndex: 'periodPrice',
    align: 'center',
    width: 230,
  },
]);
const handleSetStock = () => {
  if (!formData.value.stock){
    return;
  }
  stockData.value.periodList = stockData.value.periodList.map((item: any) => {
    return {
      ...item,
      periodStock: formData.value.stock,
    };
  });
  formData.value.stock = null;
};

const handleChangeDate = async (val: any) => {
  console.log(dayjs(val).format('YYYY-MM-DD'), 'val');
  // 如果val小于当前日期
  if (dayjs(val).format('YYYY-MM-DD') < dayjs().format('YYYY-MM-DD')) {
    modelStockApi.setState({
      footer: false,
    });
  } else {
    modelStockApi.setState({
      footer: true,
    });
  }
  const res = await getTicketPeriodInfo({
    ticketId: stockData.value.ticketId,
    playDate: dayjs(val).format('YYYY-MM-DD'),
  });
  stockData.value.periodList = res.periodList.map((item: any) => {
    return {
      ...item,
      time: `${item.beginTime} - ${item.endTime}`,
      isCustomStock: false,
      isCustomPrice: false,
    };
  });
};

const handleSetTime = () => {
  modelTimeApi.setData(stockData.value.periodList).open();
};
const handleTimeConfirm = (val: any) => {
  stockData.value.periodList = val.map((item: any) => {
    return {
      ...item,
      time: `${item.beginTime} - ${item.endTime}`,
      isCustomStock: false,
      isCustomPrice: false,
    };
  });
};
const disabledDate = (current: any) => {
  // 只能选择purchaseBeginDate-purchaseEndDate之间的日期
  return (
    current < dayjs(dateRange.value.purchaseBeginDate) ||
    current > dayjs(dateRange.value.purchaseEndDate)
  );
};
const handleSubmit = async () => {
  let submitData = {
    playDate: dayjs(stockData.value.playDate).format('YYYY-MM-DD'),
    ticketId: stockData.value.ticketId,
    periodList: stockData.value.periodList,
  };
  console.log(submitData, 'submitData');
  updateTicketPeriod(submitData).then(() => {
    message.success('设置成功');
    modelStockApi.close();
  });
};
</script>

<template>
  <ModelStock class="w-[800px]">
    <Form
      layout="inline"
      :model="formData"
      class="mb-2"
      :labelCol="{
        style: {
          fontSize: '14px',
          lineHeight: '22px',
          fontWeight: '500',
          textAlign: 'right',
          marginRight: '8px',
          width: '100px',
        },
      }"
    >
      <FormItem label="库存统一设置">
        <div class="flex items-center">
          <InputNumber
            :min="0"
            :max="1000000"
            placeholder="请输入库存"
            class="w-full"
            v-model:value="formData.stock"
          />
          <Button type="primary" class="ml-2" @click="handleSetStock"
            >设置</Button
          >
        </div>
      </FormItem>
      <FormItem label="选择日期">
        <DatePicker
          v-model:value="stockData.playDate"
          class="w-full"
          :locale="locale"
          placeholder="请选择日期"
          format="YYYY-MM-DD"
          :allowClear="false"
          :disabledDate="disabledDate"
          @change="handleChangeDate"
        ></DatePicker>
      </FormItem>
      <FormItem>
        <div class="w-full text-right">
          <Button type="primary" @click="handleSetTime">时段设置</Button>
        </div>
      </FormItem>
    </Form>
    <Table
      :columns="columns"
      :dataSource="stockData.periodList"
      :pagination="false"
      bordered
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'isCustomStock'">
          <Switch v-model:checked="record.isCustomStock" />
        </template>
        <template v-if="column.dataIndex === 'isCustomPrice'">
          <Switch v-model:checked="record.isCustomPrice" />
        </template>
        <template v-if="['periodStock'].includes(column.dataIndex)">
          <InputNumber
            :min="0"
            :max="1000000"
            placeholder="请输入库存"
            class="w-full"
            v-model:value="record[column.dataIndex]"
            v-if="record.isCustomStock"
          />
          <template v-else>{{ text == -1 ? '' : text }}</template>
        </template>
        <template v-if="['periodPrice'].includes(column.dataIndex)">
          <InputNumber
            :min="0"
            :max="1000000"
            placeholder="请输入价格"
            class="w-full"
            v-model:value="record[column.dataIndex]"
            v-if="record.isCustomPrice"
          />
          <template v-else>{{ text == -1 ? '' : text }}</template>
        </template>
      </template>
    </Table>
  </ModelStock>
  <ModelTime @confirm="handleTimeConfirm"></ModelTime>
</template>
