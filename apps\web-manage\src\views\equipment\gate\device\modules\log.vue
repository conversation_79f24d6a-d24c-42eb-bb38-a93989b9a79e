<script setup lang="ts">
import { useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useLogColumns, useGridFormLogSchema } from '../data'
import { getGateVerificationList } from '#/api'
const [Model, modelApi] = useVbenModal({
    showCancelButton: false,
    confirmText: '关闭',
    async onConfirm() {
        modelApi.close();
    },
    onOpenChange(isOpen) {
        const data = modelApi.getData<any>();
        if (isOpen) {
            gridApi.setGridOptions({
                proxyConfig: {
                    ajax: {
                        query: async ({ page }: any, formValues: any) => {
                            const res: any = await getGateVerificationList({
                                page: page.currentPage,
                                pageSize: page.pageSize,
                                machineId: data.id,
                                ...formValues,
                            });
                            return {
                                items: res.list,
                                total: res.total,
                            };
                        },
                    },
                },
            })
        }
    },
});
const [Grid, gridApi] = useVbenVxeGrid({
    formOptions: {
        fieldMappingTime: [['verificationDate', ['verificationStartDate', 'verificationEndDate']]],
        schema: useGridFormLogSchema(),
        collapsed: true,
        submitOnChange: true,
        submitOnEnter: true,
        collapsedRows: 1,
        wrapperClass: 'grid-cols-3 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-3 gap-2',
        showCollapseButton: true,
    },
    separator: false,
    gridOptions: {
        columns: useLogColumns(),
        height: 600,
        keepSource: true,
        proxyConfig: {
            ajax: {
                query: async ({ page }, formValues) => {
                    const res: any = await getGateVerificationList({
                        page: page.currentPage,
                        pageSize: page.pageSize,
                        ...formValues,
                    });
                    return {
                        items: res.list,
                        total: res.total,
                    };
                },
            },
        },
        rowConfig: {
            keyField: 'id',
        },

        toolbarConfig: {
            custom: true,
            export: false,
            refresh: { code: 'query' },
            search: true,
            zoom: true,
            enabled: false,
        },
    } as VxeTableGridOptions<any>,
});
</script>

<template>
    <Model class="w-[50%]" title="核销记录">
        <Grid />
    </Model>
</template>