import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getTicketList(params: Recordable<any>) {
  return requestClient.get('/tkt/ticket/list', { params });
}

async function getTicketAllList(params: Recordable<any>) {
  return requestClient.get('/tkt/ticket/all', { params });
}

async function getTicketInfo(id: string) {
  return requestClient.get('/tkt/ticket/info', { params: { id } });
}

async function createTicket(data: Recordable<any>) {
  return requestClient.post('/tkt/ticket/create', data);
}

async function updateTicket(data: Recordable<any>) {
  return requestClient.post('/tkt/ticket/update', data);
}

async function deleteTicket(id: string) {
  return requestClient.post('/tkt/ticket/delete', { id });
}

async function changeTicketStatus(data: Recordable<any>) {
  return requestClient.post('/tkt/ticket/changeStatus', data);
}

// 获取分时配置
async function getTicketPeriodInfo(params: Recordable<any>) {
  return requestClient.get('/tkt/ticket/periodInfo', { params });
}

// 分时配置
async function updateTicketPeriod(data: Recordable<any>) {
  return requestClient.post('/tkt/ticket/periodInfo', data);
}

export {
  getTicketList,
  getTicketAllList,
  getTicketInfo,
  createTicket,
  updateTicket,
  deleteTicket,
  changeTicketStatus,
  getTicketPeriodInfo,
  updateTicketPeriod,
};
