<script lang="ts" setup>
import type { UploadProps } from 'ant-design-vue';

import { ref, watch } from 'vue';

import { message, Modal, Upload } from 'ant-design-vue';

import { customOssUpload } from '#/utils/aliyun-oss';

import { IconifyIcon } from '@vben/icons';

// ===========================================================================
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 上传文件的类型标签，默认为所有
  accept: {
    type: String,
    default: '.',
  },
  // 上传文件的最大数量，默认为 9
  maxCount: {
    type: Number,
    default: 9,
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true,
  },
  // 单个文件的最大体积限制，默认为 10M
  // 图片10M  音频50M  视频200M  文件20M
  maxFileSize: {
    type: Number,
    default: 10,
  },
  // 上传文件的类型标签，默认为 fileTag
  fileTypeTag: {
    type: String,
    default: 'fileTag',
  },
  showUploadList: {
    type: Boolean,
    default: true,
  },
  listType: {
    type: String,
    default: 'picture-card',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:modelValue']);

// ===========================================================================
const previewVisible = ref(false);
const previewImage = ref('');
const previewType = ref('img');

const fileList: any = ref<UploadProps['fileList']>([]);

watch(
  () => props.modelValue,
  (newVal) => {
    fileList.value = newVal;
  },
  {
    immediate: true,
    deep: true,
  },
);

// 预览 ========================================================================================
const handleCancel = () => {
  previewVisible.value = false;
};
const handlePreview = async (file: UploadProps['fileList'][number]) => {
  // 判断文件类型
  const fileName = file.name || file.url;
  const fileExtension = fileName.split('.').pop()?.toLowerCase();
  
  // 判断文件类型，如果是图片，则预览图片
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension)) {
    previewType.value = 'img';
  }
  // 判断文件类型，如果是视频，则预览视频
  else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(fileExtension)) {
    previewType.value = 'video';
  }
  // 判断文件类型，如果是音频，则预览音频
  else if (['mp3', 'wav', 'ogg', 'aac'].includes(fileExtension)) {
    previewType.value = 'audio';
  }
  else {
    previewType.value = 'img'; // 默认为图片
  }
  
  previewImage.value = file.url || file.preview;
  previewVisible.value = true;
};

// 上传校验 ========================================================================================
const prevent = (key: any) => {
  if (key) {
    setTimeout(() => {
      fileList.value = fileList.value.filter(
        (item: any) => item.lastModified !== key,
      );
    }, 300);
  }
};
const beforeUpload: UploadProps['beforeUpload'] = async (file: any) => {
  if (file.size > props.maxFileSize * 1024 * 1024) {
    message.error(`文件 ${file.name} 大小超过 ${props.maxFileSize}M`);
    prevent(file.lastModified);
    return false;
  }
  return true;
};

// 自定义上传 ===========================================================================================
const customRequest = async (file: any) => {
  let res = await customOssUpload({
    file: file.file,
    fileTypeTag: props.fileTypeTag,
    chunkSize: 1 * 1024 * 1024, // 1M
  });
  // 先移除同 uid 的旧项（如 status: uploading）
  fileList.value = fileList.value.filter(
    (item: any) => item.uid !== file.file.uid,
  );
  fileList.value.push({
    uid: file.file.uid,
    name: file.file.name,
    status: 'done',
    url: res,
  });
  emit('update:modelValue', fileList.value);
};

// 删除 ========================================================================================
const handleRemove = (file: any) => {
  fileList.value = fileList.value.filter((item: any) => item.uid !== file.uid);
  emit('update:modelValue', fileList.value);
};
</script>
<template>
  <div class="clearfix">
    <Upload v-model:file-list="fileList" action="" :accept="accept" :before-upload="beforeUpload"
      :custom-request="customRequest" :listType="listType" :max-count="maxCount" :disabled="disabled"
      :multiple="multiple" :showUploadList="showUploadList" @preview="handlePreview" @remove="handleRemove">
      <slot>
        <div v-if="fileList.length < maxCount">
          <IconifyIcon icon="mdi:upload" class="size-4" />
        </div>
      </slot>
    </Upload>
    <Modal :open="previewVisible" title="预览" :footer="null" @cancel="handleCancel" width="500px">
      <div class="flex h-[500px] items-center justify-center">
        <img v-if="previewType === 'img'" alt="example" style="width: 100%; height: 100%; object-fit: contain" :src="previewImage" />
        <video v-else-if="previewType === 'video'" controls style="width: 100%; height: 100%; object-fit: contain" :src="previewImage" />
        <audio v-else-if="previewType === 'audio'" controls style="width: 100%;" :src="previewImage" />
      </div>
    </Modal>
  </div>
</template>
<style scoped lang="scss"></style>


