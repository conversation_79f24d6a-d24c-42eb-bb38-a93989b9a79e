import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getUserCardList(params: Recordable<any>) {
  return requestClient.get('/tkt/userCard/list', { params });
}

async function getUserCardInfo(params: Recordable<any>) {
  return requestClient.get('/tkt/userCard/info', { params });
}

async function changeUserCardStatus(data: Recordable<any>) {
  return requestClient.post('/tkt/userCard/changeStatus', data);
}

export { getUserCardList, getUserCardInfo, changeUserCardStatus };
