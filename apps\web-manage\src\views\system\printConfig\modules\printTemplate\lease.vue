<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import {
  Checkbox,
  Input,
  Row,
  Col,
  InputNumber,
  Divider,
  QRCode,
} from 'ant-design-vue';

const props = defineProps({
  value: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['update:value']);
// 配置项状态
let config = reactive({
  // 页面设置
  page: {
    width: 58, // 小票纸宽度(mm) - 常见58mm热敏纸
    marginTop: 2,
    marginBottom: 2,
    marginLeft: 2,
    marginRight: 2,
  },
  // 抬头设置
  header: {
    scenicName: true,
  },
  // 门票信息
  top: {
    leaseCode: true,
    orderNumber: true,
    saleWindow: true,
    saleTime: true,
    operator: true,
  },
  // 中部信息
  middle: {
    leaseName: true,
    deposit: true,
    rent: true,
  },
  // 底部设置
  bottom: {
    receivedAmount: true,
    actualAmount: true,
    paymentMethod: true,
    change: true,
  },
  // 尾注设置
  footer: {
    endnote: true,
    endnoteText: '',
  },
});

// 计算预览区样式 - 按实际小票比例
const previewStyle = computed(() => ({
  width: `${config.page.width * 4}px`, // 58mm * 4 = 232px
  padding: `${config.page.marginTop * 4}px ${config.page.marginRight * 4}px ${config.page.marginBottom * 4}px ${config.page.marginLeft * 4}px`,
  fontFamily: 'monospace', // 使用等宽字体模拟小票打印效果
}));

watch(
  () => config,
  (newConfig) => {
    emit('update:value', {
      margins: [
        newConfig.page.marginLeft * 4,
        newConfig.page.marginTop * 4,
        newConfig.page.marginRight * 4,
        newConfig.page.marginBottom * 4,
      ],
      width: newConfig.page.width * 4,
      templateConfig: newConfig,
    });
  },
  { deep: true },
);
watch(
  () => props.value,
  (newValue) => {
    if (newValue && newValue.templateConfig) {
      Object.assign(config, newValue.templateConfig);
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
</script>

<template>
  <div class="flex gap-6">
    <!-- 左侧预览区 -->
    <div class="w-1/3">
      <h3 class="mb-4 text-lg font-semibold">模板设计</h3>
      <div
        class="flex min-h-[600px] items-start justify-center border border-gray-300 bg-gray-50 p-4"
      >
        <!-- 小票预览 -->
        <div class="border bg-white shadow-lg" :style="previewStyle">
          <!-- 抬头 -->
          <div v-if="config.header.scenicName" class="mb-1 text-center">
            <div
              v-if="config.header.scenicName"
              class="text-[14px] font-bold leading-[1.8]"
            >
              演示景区
            </div>
          </div>

          <!-- 顶部信息 -->
          <div class="text-[14px] leading-normal">
            <div v-if="config.top.leaseCode" class="mb-2 flex justify-center">
              <QRCode value="门票码" :size="100" type="svg" :bordered="false" />
            </div>
            <div v-if="config.top.orderNumber" class="mb-1.5 flex">
              <span
                class="block w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >订单号</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">12345678990</span>
            </div>
            <div v-if="config.top.saleWindow" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >租赁窗口</span
              >
              <span class="opacity-50">：</span>

              <span class="flex-1">大门1号窗口</span>
            </div>
            <div v-if="config.top.saleTime" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >租赁时间</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">2025-07-23 11:38:38</span>
            </div>
            <div v-if="config.top.operator" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >操作员</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">租赁员A</span>
            </div>
          </div>
          <!-- 分割线 -->
          <Divider
            v-if="
              config.top.leaseCode ||
              config.top.orderNumber ||
              config.top.saleWindow ||
              config.top.saleTime ||
              config.top.operator
            "
            class="!my-2 !text-[14px]"
            dashed
          />

          <!-- 中部信息 -->
          <div class="text-[14px] leading-tight">
            <div
              v-if="
                config.middle.leaseName ||
                config.middle.deposit ||
                config.middle.rent
              "
              class="mb-1"
            >
              <div class="mb-1 flex pb-1">
                <span
                  v-if="config.middle.leaseName"
                  class="truncate"
                  :class="[
                    config.middle.deposit && config.middle.rent
                      ? 'w-[60%]'
                      : config.middle.deposit || config.middle.rent
                        ? 'w-[70%]'
                        : 'w-full',
                  ]"
                  >租赁物品</span
                >
                <span
                  v-if="config.middle.deposit"
                  class="text-center"
                  :class="[
                    config.middle.leaseName && config.middle.rent
                      ? 'w-[25%]'
                      : config.middle.leaseName || config.middle.rent
                        ? 'w-[30%]'
                        : 'w-full',
                  ]"
                  >押金</span
                >
                <span
                  v-if="config.middle.rent"
                  class="text-center"
                  :class="[
                    config.middle.leaseName && config.middle.deposit
                      ? 'w-[15%]'
                      : config.middle.leaseName || config.middle.deposit
                        ? 'w-[30%]'
                        : 'w-full',
                  ]"
                  >租金</span
                >
              </div>
              <div class="mb-1 flex">
                <span
                  v-if="config.middle.leaseName"
                  class="truncate"
                  :class="[
                    config.middle.deposit && config.middle.rent
                      ? 'w-[60%]'
                      : config.middle.deposit || config.middle.rent
                        ? 'w-[70%]'
                        : 'w-full',
                  ]"
                  >这里是租赁物品名称</span
                >
                <span
                  v-if="config.middle.deposit"
                  class="text-center"
                  :class="[
                    config.middle.leaseName && config.middle.rent
                      ? 'w-[25%]'
                      : config.middle.leaseName || config.middle.rent
                        ? 'w-[30%]'
                        : 'w-full',
                  ]"
                  >50.00</span
                >
                <span
                  v-if="config.middle.rent"
                  class="text-center"
                  :class="[
                    config.middle.leaseName && config.middle.deposit
                      ? 'w-[15%]'
                      : config.middle.leaseName || config.middle.deposit
                        ? 'w-[30%]'
                        : 'w-full',
                  ]"
                  >2</span
                >
              </div>
              <div class="flex">
                <span
                  v-if="config.middle.leaseName"
                  class="truncate"
                  :class="[
                    config.middle.deposit && config.middle.rent
                      ? 'w-[60%]'
                      : config.middle.deposit || config.middle.rent
                        ? 'w-[70%]'
                        : 'w-full',
                  ]"
                  >这里是租赁物品名称</span
                >
                <span
                  v-if="config.middle.deposit"
                  class="text-center"
                  :class="[
                    config.middle.leaseName && config.middle.rent
                      ? 'w-[25%]'
                      : config.middle.leaseName || config.middle.rent
                        ? 'w-[30%]'
                        : 'w-full',
                  ]"
                  >100.00</span
                >
                <span
                  v-if="config.middle.rent"
                  class="text-center"
                  :class="[
                    config.middle.leaseName && config.middle.deposit
                      ? 'w-[15%]'
                      : config.middle.leaseName || config.middle.deposit
                        ? 'w-[30%]'
                        : 'w-full',
                  ]"
                  >1</span
                >
              </div>
            </div>
          </div>

          <!-- 分割线 -->
          <Divider
            v-if="
              config.middle.leaseName ||
              config.middle.deposit ||
              config.middle.rent
            "
            class="!my-2 !text-[14px]"
            dashed
          />
          <!-- 底部信息 -->
          <div class="text-[14px] leading-normal">
            <div v-if="config.bottom.receivedAmount" class="mb-1.5 flex">
              <span class="w-[56px] text-justify opacity-50">应收金额</span>
              <span class="opacity-50">：</span>
              <span class="flex-1">200.00</span>
            </div>
            <div v-if="config.bottom.actualAmount" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >实收金额</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">200.00</span>
            </div>
            <div v-if="config.bottom.paymentMethod" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >结算方式</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">扫码</span>
            </div>
            <div v-if="config.bottom.change" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >找零</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">0.00</span>
            </div>
          </div>

          <!-- 分割线 -->
          <Divider
            v-if="config.footer.endnote"
            class="!my-2 !text-[14px]"
            dashed
          />

          <!-- 尾注 -->
          <div
            v-if="config.footer.endnote"
            class="text-center text-[14px] leading-tight"
          >
            <div v-if="config.footer.endnote" class="mb-1">
              {{
                config.footer.endnoteText ||
                '尾注：尾注限制100字，居中对齐，超出换行'
              }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧配置区 -->
    <div class="w-2/3">
      <div class="space-y-6">
        <!-- 页面设置 -->
        <div>
          <h4 class="mb-3 font-semibold">页面设置</h4>
          <Row :gutter="16">
            <Col :span="24">
              <div class="mb-2 flex items-center gap-2">
                <span class="w-18 text-[14px]">纸张宽度：</span>
                <InputNumber
                  v-model:value="config.page.width"
                  :min="40"
                  :max="80"
                  size="small"
                />
                <span class="text-[14px] text-gray-500"
                  >mm (常用: 58mm/80mm)</span
                >
              </div>
            </Col>
          </Row>
          <Row :gutter="8">
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-15 text-[14px]">上边距：</span>
                <InputNumber
                  v-model:value="config.page.marginTop"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-[14px] text-gray-500">mm</span>
              </div>
            </Col>
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-15 text-[14px]">下边距：</span>
                <InputNumber
                  v-model:value="config.page.marginBottom"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-[14px] text-gray-500">mm</span>
              </div>
            </Col>
          </Row>
          <Row :gutter="8" class="mt-2">
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-15 text-[14px]">左边距：</span>
                <InputNumber
                  v-model:value="config.page.marginLeft"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-[14px] text-gray-500">mm</span>
              </div>
            </Col>
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-15 text-[14px]">右边距：</span>
                <InputNumber
                  v-model:value="config.page.marginRight"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-[14px] text-gray-500">mm</span>
              </div>
            </Col>
          </Row>
        </div>

        <!-- 抬头设置 -->
        <div>
          <h4 class="mb-3 font-semibold">抬头设置</h4>
          <Row :gutter="16">
            <Col :span="6">
              <Checkbox v-model:checked="config.header.scenicName"
                >景区名称</Checkbox
              >
            </Col>
          </Row>
        </div>

        <!-- 租赁信息 -->
        <div>
          <h4 class="mb-3 font-semibold">租赁信息</h4>
          <Row :gutter="16">
            <Col :span="6">
              <Checkbox v-model:checked="config.top.leaseCode">租赁码</Checkbox>
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.top.orderNumber"
                >订单号</Checkbox
              >
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.top.saleWindow"
                >租赁窗口</Checkbox
              >
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.top.saleTime"
                >租赁时间</Checkbox
              >
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.top.operator">操作员</Checkbox>
            </Col>
          </Row>
        </div>

        <!-- 中部信息 -->
        <div>
          <h4 class="mb-3 font-semibold">中部信息</h4>
          <Row :gutter="16">
            <Col :span="6">
              <Checkbox v-model:checked="config.middle.leaseName"
                >租赁物品</Checkbox
              >
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.middle.deposit">押金</Checkbox>
            </Col>
            <Col :span="6">
              <Checkbox v-model:checked="config.middle.rent">租金</Checkbox>
            </Col>
          </Row>
        </div>

        <!-- 底部设置 -->
        <div>
          <h4 class="mb-3 font-semibold">底部设置</h4>
          <Row :gutter="16">
            <Col :span="12">
              <Checkbox v-model:checked="config.bottom.receivedAmount"
                >应收金额</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.bottom.actualAmount"
                >实收金额</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.bottom.paymentMethod"
                >结算方式</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.bottom.change">找零</Checkbox>
            </Col>
          </Row>
        </div>

        <!-- 尾注设置 -->
        <div>
          <h4 class="mb-3 font-semibold">尾注设置</h4>
          <div class="flex items-center gap-2">
            <Checkbox v-model:checked="config.footer.endnote" class="w-[80px]"
              >尾注</Checkbox
            >
            <Input
              v-model:value="config.footer.endnoteText"
              placeholder="请输入尾注内容"
              size="small"
              :maxlength="100"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
