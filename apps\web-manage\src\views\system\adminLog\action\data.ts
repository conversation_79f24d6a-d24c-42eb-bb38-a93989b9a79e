import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import dayjs from 'dayjs';
import { getAllUserList } from '#/api';
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'title',
      label: '操作名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入操作名称',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: [
          {
            label: '成功',
            value: 1,
          },
          {
            label: '失败',
            value: 2,
          },
        ],
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '用户',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.name,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllUserList,
        placeholder: '请选择用户',
        allowClear: true,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'date',
      label: '日期',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['开始日期', '结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
      defaultValue: [dayjs().startOf('month'), dayjs().endOf('month')], // 默认当月
    },
  ];
}

export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'title',
      title: '操作名称',
      width: 120,
    },
    {
      field: 'module',
      title: '操作模块',
      width: 120,
    },
    {
      field: 'userName',
      title: '用户',
      width: 100,
      formatter: ({ row }: any) => {
        return row.userInfo.name || '--';
      },
    },
    {
      title: '登录IP',
      field: 'ip',
      width: 120,
    },
    {
      title: '状态码',
      field: 'code',
      width: 120,
    },
    {
      title: '请求时间',
      field: 'requestTime',
      width: 150,
    },
    {
      title: '提示消息',
      field: 'message',
      minWidth: 120,
    },
    {
      title: '响应时间',
      field: 'responseTime',
      width: 150,
    },
    {
      title: '耗时',
      field: 'duration',
      width: 120,
    },
    {
      title: '请求地址',
      field: 'uri',
      width: 120,
    },
    {
      title: '请求方式',
      field: 'method',
      width: 120,
    },
    {
      field: 'status',
      title: '状态',
      width: 100,
      cellRender: {
        name: 'CellTag',
        options: [
          { color: 'success', label: '成功', value: 1 },
          { color: 'error', label: '失败', value: 2 },
        ],
      },
    },
    {
      field: 'operation',
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: '操作',
      width: 120,
    },
  ];
}
