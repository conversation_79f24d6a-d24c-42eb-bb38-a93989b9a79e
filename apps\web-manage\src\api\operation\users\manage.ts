import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getUserList(params: Recordable<any>) {
  return requestClient.get('/op/users/list', { params });
}
async function getUserInfo(params: Recordable<any>) {
  return requestClient.get('/op/users/info', { params });
}

async function changeUserStatus(data: Recordable<any>) {
  return requestClient.post('/op/users/changeStatus', data);
}

export { getUserList, getUserInfo, changeUserStatus };
