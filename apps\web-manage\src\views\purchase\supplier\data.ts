import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

// 搜索表单
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '供应商名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入供应商名称',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '联系人电话',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入联系人电话',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'distributorType',
      label: '类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择供应商类型',
        options: [
          { label: '采购供应商', value: 1 },
          { label: '委外供应商', value: 2 },
        ],
        allowClear: true,
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'serialNumber',
      title: '供应商编号',
      width: 150,
      fixed: 'left',
    },
    {
      field: 'distributorName',
      title: '供应商名称',
      width: 150,
      fixed: 'left',
    },
    {
      field: 'distributorType',
      formatter: ({ row }: any) => {
        return row.distributorType == 1 ? '采购供应商' : '委外供应商';
      },
      title: '供应商类型',
      width: 150,
    },
    {
      field: 'contactName',
      minWidth: 100,
      title: '联系人',
    },
    {
      field: 'contactPhone',
      minWidth: 100,
      title: '联系电话',
    },
    {
      field: 'principal',
      title: '对接负责人',
      width: 100,
    },
    {
      field: 'region',
      title: '地区',
      width: 150,
    },
    {
      field: 'address',
      title: '详细地址',
      width: 100,
    },
    {
      field: 'source',
      title: '客户来源',
      width: 100,
    },
    {
      field: 'tags',
      title: '标签',
      width: 'auto',
      slots: {
        default: 'tableTags',
      },
    },
    {
      field: 'remark',
      minWidth: 100,
      title: '备注',
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 165,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'distributorName',
          nameTitle: '供应商',
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 130,
    },
  ];
}
