<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { getOrderVerifyLog } from '#/api';
import { Table } from 'ant-design-vue';
import { useVerifyTableSchema } from '../data';

const [Modal, modalApi] = useVbenModal({
  showCancelButton: false,
  async onConfirm() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    const data = modalApi.getData<any>();
    if (isOpen) {
      params.value = { ...params.value, ...data };
      getLogList();
    }
  },
});
const params = ref<any>({
  orderDetailId: '',
  orderId: '',
  orderItemId: '',
  page: 1,
  pageSize: 10,
});
const orderVerifyLog = ref<any>([]);
const total = ref(0);
const getLogList = async () => {
  const res = await getOrderVerifyLog(params.value);
  orderVerifyLog.value = res.list;
  total.value = res.total;
};
</script>
<template>
  <Modal class="w-[1000px]" title="核销记录">
    <Table
      :columns="useVerifyTableSchema()"
      :dataSource="orderVerifyLog"
      :pagination="{
        current: params.page,
        pageSize: params.pageSize,
        total: total,
        onChange: (page, pageSize) => {
          params.page = page;
          params.pageSize = pageSize;
          getLogList();
        },
        showTotal: (total) => `共 ${total} 条`,
      }"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex == 'adminUserInfo'">
          <p v-if="record.adminUserInfo">
            {{ record.adminUserInfo?.name }}<br />{{
              record.adminUserInfo?.phone
            }}
          </p>
          <p v-else>--</p>
        </template>
      </template>
    </Table>
  </Modal>
</template>
