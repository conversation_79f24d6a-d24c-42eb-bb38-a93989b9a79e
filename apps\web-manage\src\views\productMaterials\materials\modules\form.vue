<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import { computed, ref, h } from 'vue';
import { getPopupContainer } from '@vben/utils';
import { useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { customOssUpload } from '#/utils/aliyun-oss';
import { useVbenForm } from '#/adapter/form';
import { Button, Modal } from 'ant-design-vue';
import {
  allType,
  createProduct,
  updateProduct,
} from '#/api/productMaterials/materials';
import { allSupplier } from '#/api/purchase';
import { modeText } from '../data';

const emits = defineEmits(['success']);

const props = defineProps({
  mode: {
    type: Number,
    default: 3,
  },
});

const formData = ref<any>();

const useFormSchema: any = [
  {
    component: 'Divider',
    fieldName: 'divider1',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-0',
    hideLabel: true,
    renderComponentContent() {
      return {
        default: () => '基础信息',
      };
    },
  },
  {
    component: 'ApiTreeSelect',
    componentProps: {
      api: allType,
      params: {
        mode: props.mode,
      },
      class: 'w-full',
      filterTreeNode(input: string, node: Recordable<any>) {
        if (!input || input.length === 0) {
          return true;
        }
        const title: string = node.typeName ?? '';
        if (!title) return false;
        return title.includes(input);
      },
      getPopupContainer,
      labelField: 'typeName',
      showSearch: true,
      treeDefaultExpandAll: true,
      valueField: 'id',
      childrenField: 'children',
      placeholder: '请选择' + modeText(props.mode) + '类别',
      allowClear: true,
    },
    fieldName: 'typeId',
    label: '类别',
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: [
        { label: '自产', value: 1 },
        { label: '采购', value: 2 },
        { label: '委外', value: 3 },
      ],
      optionType: 'default',
      onChange(e: any) {
        if (e.target.value === 2) {
          getSupplierList(1);
        } else if (e.target.value === 3) {
          getSupplierList(2);
        }
      },
    },
    defaultValue: 1,
    fieldName: 'source',
    label: modeText(props.mode) + '来源',
  },
  {
    component: 'Input',
    fieldName: 'productName',
    label: modeText(props.mode) + '名称',
    componentProps: {
      placeholder: '请输入' + modeText(props.mode) + '名称',
      allowClear: true,
    },
    rules: 'required',
  },
  {
    component: 'Select',
    dependencies: {
      show: (values: any) => {
        return ![1].includes(values.source);
      },
      triggerFields: ['source'],
    },
    componentProps: {
      class: 'w-full',
      placeholder: '请选择供应商',
      allowClear: true,
      options: computed(() =>
        supplierList.value.map((item: any) => ({
          label: item.distributorName,
          value: item.id,
        })),
      ),
    },
    fieldName: 'distributorId',
    label: '供应商',
  },
  {
    component: 'Input',
    label: '规格型号',
    fieldName: 'specification',
    componentProps: {
      placeholder: '请输入规格型号',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    label: modeText(props.mode) + '颜色',
    fieldName: 'color',
    componentProps: {
      placeholder: '请输入' + modeText(props.mode) + '颜色',
      allowClear: true,
    },
  },
  // {
  //   component: 'Input',
  //   label: '仓储单位',
  //   fieldName: 'storageUnit',
  // },
  {
    component: 'Input',
    label: '单位',
    fieldName: 'useUnit',
    componentProps: {
      placeholder: '请输入单位',
      allowClear: true,
    },
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '未启用', value: 0 },
      ],
    },
    defaultValue: 1,
    fieldName: 'status',
    label: modeText(props.mode) + '状态',
  },
  {
    component: 'Upload',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-6',
    componentProps: {
      // 更多属性见：https://ant.design/components/upload-cn
      // 允许上传的文件类型
      accept: 'image/*',
      // 使用自定义的阿里云上传处理函数
      customRequest: (options: any) =>
        customOssUpload({
          ...options,
          // 设置分片大小为1MB
          chunkSize: 1 * 1024 * 1024,
          fileTypeTag: 'materials',
          onSuccess(res: any, file: any) {
            console.log(res, file, 'upload');
            // 获取上传成功后的文件地址
            const url = res.url;
            // 将文件地址设置到表单中 - 创建新对象而不是直接修改
            formData.value = { ...formData.value, productImg: url };
            // 调用原始的onSuccess回调，确保Upload组件状态更新
            options.onSuccess && options.onSuccess(res, file);
          },
        }),
      disabled: false,
      maxCount: 9,
      multiple: false,
      showUploadList: true,
      // 上传列表的内建样式，支持四种基本样式 text, picture, picture-card 和 picture-circle
      listType: 'picture-card',
      onPreview(file: any) {
        console.log(file, 'file');
        // 打开弹窗图片预览
        Modal.info({
          centered: true,
          title: '图片预览',
          closable: true,
          content: h('img', {
            src: file.url,
            style: 'width: 100%; height: 100%;',
          }),
          footer: null,
        });
      },
    },
    fieldName: 'productImg',
    label: modeText(props.mode) + '图片',
    renderComponentContent: () => {
      return {
        default: () =>
          h(IconifyIcon, {
            class: 'size-4',
            icon: 'mdi:upload',
          }),
      };
    },
  },
  {
    component: 'Upload',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-6',
    componentProps: {
      // 允许上传的文件类型
      accept: '.',
      // 使用自定义的阿里云上传处理函数
      customRequest: (options: any) =>
        customOssUpload({
          ...options,
          // 设置分片大小
          chunkSize: 1 * 1024 * 1024,
          fileTypeTag: 'materials',
          onSuccess(res: any, file: any) {
            console.log(res, 'upload');
            // 获取上传成功后的文件地址
            const url = res.url;
            // 将文件地址设置到表单中 - 创建新对象而不是直接修改
            formData.value = { ...formData.value, attachments: url };
            // 调用原始的onSuccess回调，确保Upload组件状态更新
            options.onSuccess && options.onSuccess(res, file);
          },
        }),
      disabled: false,
      maxCount: 9,
      multiple: false,
      showUploadList: true,
      listType: 'text',
    },
    fieldName: 'attachments',
    label: '附件图纸',
    renderComponentContent: () => {
      return {
        default: () =>
          h(
            Button,
            {},
            {
              default: () => {
                const content = [];
                content.push(
                  h(IconifyIcon, {
                    icon: 'mdi:upload',
                    class: 'size-4',
                  }),
                );
                content.push('上传');
                return content;
              },
            },
          ),
      };
    },
  },
  {
    component: 'Textarea',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-0',
    fieldName: 'remark',
    label: modeText(props.mode) + '备注',
    componentProps: {
      placeholder: '请输入' + modeText(props.mode) + '备注',
      allowClear: true,
    },
  },
  {
    component: 'Divider',
    fieldName: 'divider1',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-0',
    hideLabel: true,
    renderComponentContent() {
      return {
        default: () => '价格信息',
      };
    },
  },
  {
    component: 'Input',
    fieldName: 'costPrice',
    label: '成本单价',
    componentProps: {
      placeholder: '请输入成本单价',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'salePrice',
    label: '销售价格',
    componentProps: {
      placeholder: '请输入销售价格',
      allowClear: true,
    },
  },
  {
    component: 'Divider',
    fieldName: 'divider1',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-0',
    hideLabel: true,
    renderComponentContent() {
      return {
        default: () => '其他信息',
      };
    },
  },
  {
    component: 'TextEditor',
    fieldName: 'productDesc',
    formItemClass: 'col-span-1 md:col-span-2 lg:col-span-3 pb-0',
    label: modeText(props.mode) + '简介',
    componentProps: {
      // value: formData.value.productDesc,
      height: '300px',
      fileTypeTag: 'materials',
    },
  },
];

// 表单配置
const [Form, formApi] = useVbenForm({
  schema: useFormSchema,
  showDefaultActions: false,
  layout: 'horizontal',
  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
});
// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const values = await formApi.getValues();
    console.log(values, 'values');
    if (values.productImg) {
      values.productImg = values.productImg
        .map((item: any) => {
          return item.response.url;
        })
        .join(',');
    }
    if (values.attachments) {
      values.attachments = values.attachments
        .map((item: any) => {
          return item.response.url;
        })
        .join(',');
    }
    console.log(values, 'values');
    modelApi.lock();
    (id.value
      ? updateProduct({ id: id.value, mode: props.mode, ...values })
      : createProduct({ ...values, mode: props.mode })
    )
      .then(() => {
        emits('success');
        modelApi.close();
      })
      .catch(() => {
        modelApi.unlock();
      });
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modelApi.getData<any>();
      formApi.resetForm();
      if (data) {
        // 创建新对象而不是直接赋值
        const newFormData = { ...data };

        if (data.productImg) {
          newFormData.productImg = data.productImg
            .split(',')
            .map((item: any) => {
              return {
                uid: item,
                name: item,
                status: 'done',
                url: item,
                response: {
                  url: item,
                },
              };
            });
        } else {
          newFormData.productImg = [];
        }
        if (data.attachments) {
          newFormData.attachments = data.attachments
            .split(',')
            .map((item: any) => {
              return {
                uid: item,
                name: item,
                status: 'done',
                url: item,
                response: {
                  url: item,
                },
              };
            });
        } else {
          newFormData.attachments = [];
        }
        // 赋值给formData.value
        formData.value = newFormData;
        id.value = data.id;
        formApi.setValues(formData.value);
      } else {
        id.value = undefined;
      }
    }
  },
});

const submitOnEnter = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return;
  const values = await formApi.getValues();
  console.log(values, 'values');
  if (values.productImg) {
    values.productImg = values.productImg
      .map((item: any) => {
        return item.response.url;
      })
      .join(',');
  }
  if (values.attachments) {
    values.attachments = values.attachments
      .map((item: any) => {
        return item.response.url;
      })
      .join(',');
  }
  console.log(values, 'values');
  modelApi.lock();
  createProduct({ ...values, mode: props.mode })
    .then(() => {
      emits('success');
      modelApi.close();
    })
    .catch(() => {
      modelApi.unlock();
    });
};

const submitAndAdd = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return;
  const values = await formApi.getValues();
  console.log(values, 'values');
  if (values.productImg) {
    values.productImg = values.productImg
      .map((item: any) => {
        return item.response.url;
      })
      .join(',');
  }
  if (values.attachments) {
    values.attachments = values.attachments
      .map((item: any) => {
        return item.response.url;
      })
      .join(',');
  }
  console.log(values, 'values');
  modelApi.lock();
  createProduct({ ...values, mode: props.mode })
    .then(() => {
      emits('success');
      formApi.resetForm();
      modelApi.unlock();
    })
    .catch(() => {
      modelApi.unlock();
    });
};
const getModelTitle = computed(() => {
  return formData.value?.id
    ? '编辑' + modeText(props.mode)
    : '新增' + modeText(props.mode);
});

// 获取供应商列表
const supplierList = ref([]);
const getSupplierList = async (type: any) => {
  const res = await allSupplier({ distributorType: type });
  console.log(res, 'res');
  supplierList.value = res;
};
</script>
<template>
  <Model class="w-[70%]" :title="getModelTitle">
    <Form />
    <template #footer v-if="!formData?.id">
      <Button type="default" @click="modelApi.close()">取消</Button>
      <Button type="primary" @click="submitOnEnter">保存</Button>
      <Button type="primary" @click="submitAndAdd">保存并新增</Button>
    </template>
  </Model>
</template>
<style lang="scss" scoped></style>
