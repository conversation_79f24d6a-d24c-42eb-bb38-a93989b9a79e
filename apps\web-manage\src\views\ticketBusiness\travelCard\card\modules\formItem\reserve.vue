<script lang="ts" setup>
import { Switch, InputNumber } from 'ant-design-vue';

const emit = defineEmits(['blur', 'change']);

const modelValue = defineModel<[number, string]>({
  default: () => [0, ''],
});

function onChange() {
  emit('change', modelValue.value);
}
</script>
<template>
  <div class="flex h-[32px] w-full items-center gap-1">
    <div class="w-2/5">
      <Switch
        v-model:checked="modelValue[0]"
        :checkedValue="1"
        :unCheckedValue="0"
        @change="onChange"
        @blur="emit('blur', modelValue)"
        class="w-[45px]"
      />
    </div>
    <div class="flex flex-1 items-center" v-if="modelValue[0] === 1">
      <div class="mr-2 w-[120px] text-right text-sm font-[500] leading-6">
        提前预约天数
      </div>
      <InputNumber
        v-model:value="modelValue[1]"
        placeholder="请输入提前预约天数"
        allow-clear
        @change="onChange"
        @blur="emit('blur', modelValue)"
        class="w-full"
      />
    </div>
  </div>
</template>
