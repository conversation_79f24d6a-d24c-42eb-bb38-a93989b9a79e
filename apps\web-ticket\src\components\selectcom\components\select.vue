<script setup lang="ts">
import { onBeforeUnmount, ref } from 'vue';

import { clearAllAlerts, useVbenModal } from '@vben/common-ui';

// =======================================================================================
import { Button, message, Select, TabPane, Tabs } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import checkTabTem from './checkTab.vue';
import radioTabTem from './radioTab.vue';
import searchTem from './search.vue';

const props = defineProps({
  modelValue: { type: Array, default: () => [] },
  isMultiple: { type: Boolean, default: false },
  showMax: { type: Number, default: 1 },
  infoVal: { type: Object, default: () => {} },
});

const emits = defineEmits(['update:modelValue']);

// ========================================================================================
const columnsArr = ref([]);
const setInfo = () => {
  const params = {
    align: 'left',
    type: props.isMultiple ? 'checkbox' : 'radio',
  };
  const arr: any = cloneDeep(props.infoVal.columns);
  arr[0] = { ...arr[0], ...params };
  columnsArr.value = arr;
};
setInfo();

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  closeOnClickModal: false,
  onCancel() {
    closeModal();
  },
  onConfirm() {
    const arr = cloneDeep(tableSelect.value).filter((i: any) =>
      tableVal.value.includes(i[props.infoVal.keyField]),
    );
    const arr2 = cloneDeep(submitOptions.value).filter((i: any) =>
      submitVal.value.includes(i[props.infoVal.keyField]),
    );

    selectVal.value = [...tableVal.value, ...submitVal.value];
    selectOptions.value = [...arr, ...arr2];
    emits('update:modelValue', selectVal.value);
    closeModal();
  },
});

// 关闭弹窗(清除相应参数) ========================================================================================
const closeModal = () => {
  modalApi.close();
  tableVal.value = [];
  tableSelect.value = [];
};

onBeforeUnmount(() => {
  // 清除所有弹窗
  clearAllAlerts();
});

// 打开弹窗 ========================================================================================
const activeKey = ref(1);
const selectOpen = () => {
  activeKey.value = 1;
  activeKey2.value = 1;

  if (!props.infoVal.tabUrl) {
    message.warning('请先设置apiUrl');
    return;
  }

  if (!props.infoVal.labelFieldKey) {
    message.warning('请先设置labelFieldKey字段');
    return;
  }

  if (!props.infoVal.keyField) {
    message.warning('请先设置keyField字段');
    return;
  }

  if (!props.infoVal.columns || props.infoVal.columns.length === 0) {
    message.warning('请先设置表格字段');
    return;
  }

  modalApi.open();

  submitVal.value = cloneDeep(selectVal.value) || [];
  submitOptions.value = cloneDeep(selectOptions.value) || [];

  if (
    props.isMultiple &&
    submitOptions.value &&
    submitOptions.value.length > 0
  ) {
    setTimeout(() => {
      checkTabTemRef.value.setCheckboxRow(submitOptions.value);
    }, 1000);
  }
};

// 选择框显示数据 =======================================================================================
const selectVal: any = ref([]);
const selectOptions: any = ref([]);

// 已提交的数据 =======================================================================================
const submitVal: any = ref([]);
const submitOptions: any = ref([]);

// 待提交的数据 ========================================================================================
const activeKey2 = ref(1);
const tableVal: any = ref([]);
const tableSelect: any = ref([]);
const radioSelect = (val: any) => {
  if (
    submitOptions.value.some(
      (i: any) => i[props.infoVal.keyField] === val[props.infoVal.keyField],
    )
  ) {
    submitVal.value[0] = val[props.infoVal.keyField];
    tableVal.value = [];
    tableSelect.value = [];
    return;
  }

  tableVal.value[0] = val[props.infoVal.keyField];
  tableSelect.value[0] = val;
  submitVal.value = [];
};
const checkSelect = ({ type, val, isAll }: any) => {
  switch (type) {
    case 'check': {
      if (
        submitOptions.value.some(
          (i: any) => i[props.infoVal.keyField] === val[props.infoVal.keyField],
        )
      ) {
        if (submitVal.value.includes(val[props.infoVal.keyField])) {
          submitVal.value = submitVal.value.filter(
            (i: any) => i !== val[props.infoVal.keyField],
          );
        } else {
          submitVal.value.push(val[props.infoVal.keyField]);
        }
        return;
      }

      if (tableVal.value.includes(val[props.infoVal.keyField])) {
        tableVal.value = tableVal.value.filter(
          (i: any) => i !== val[props.infoVal.keyField],
        );
        tableSelect.value = tableSelect.value.filter(
          (i: any) => i[props.infoVal.keyField] !== val[props.infoVal.keyField],
        );
      } else {
        tableVal.value.push(val[props.infoVal.keyField]);
        tableSelect.value.push(val);
      }
      break;
    }

    case 'checkAll': {
      for (const item of val) {
        if (
          submitOptions.value.some(
            (i: any) =>
              i[props.infoVal.keyField] === item[props.infoVal.keyField],
          )
        ) {
          if (isAll) {
            if (!submitVal.value.includes(item[props.infoVal.keyField])) {
              submitVal.value.push(item[props.infoVal.keyField]);
            }
          } else {
            submitVal.value = [];
          }
        } else {
          if (isAll) {
            if (!tableVal.value.includes(item[props.infoVal.keyField]))
              tableVal.value.push(item[props.infoVal.keyField]);
            if (!tableSelect.value.includes(item)) tableSelect.value.push(item);
          } else {
            tableVal.value = tableVal.value.filter(
              (i: any) => i !== item[props.infoVal.keyField],
            );
            tableSelect.value = tableSelect.value.filter(
              (i: any) =>
                i[props.infoVal.keyField] !== item[props.infoVal.keyField],
            );
          }
        }
      }
      break;
    }
  }
};

// 已选 ========================================================================================
const checkTabTemRef: any = ref(null);
const radioTabTemRef: any = ref(null);
const alreadyBtn = (type: number, val: any) => {
  if (props.isMultiple) {
    if (type) {
      tableVal.value = tableVal.value.filter(
        (i: any) => i !== val[props.infoVal.keyField],
      );
      tableSelect.value = tableSelect.value.filter(
        (i: any) => i[props.infoVal.keyField] !== val[props.infoVal.keyField],
      );
    } else {
      if (submitVal.value.includes(val[props.infoVal.keyField])) {
        submitVal.value = submitVal.value.filter(
          (i: any) => i !== val[props.infoVal.keyField],
        );
      } else {
        submitVal.value.push(val[props.infoVal.keyField]);
      }
    }

    checkTabTemRef.value.clearCheckboxRow();
    const arr = cloneDeep(submitOptions.value).filter((i: any) =>
      submitVal.value.includes(i[props.infoVal.keyField]),
    );
    const arrAll = [...arr, ...tableSelect.value];
    checkTabTemRef.value.setCheckboxRow(arrAll);
  } else {
    tableVal.value = [];
    tableSelect.value = [];
    radioTabTemRef.value.clearRadioRow();
    radioTabTemRef.value.setRadioRow({});

    if (
      submitOptions.value.some(
        (i: any) => i[props.infoVal.keyField] === val[props.infoVal.keyField],
      )
    ) {
      if (submitVal.value.includes(val[props.infoVal.keyField])) {
        submitVal.value = [];
        radioTabTemRef.value.clearRadioRow();
      } else {
        submitVal.value.push(val[props.infoVal.keyField]);
        radioTabTemRef.value.setRadioRow(submitOptions.value[0]);
      }
    }
  }
};

// 搜索 =======================================================================================================
const searchBtn = (e: any) => {
  if (props.isMultiple) {
    checkTabTemRef.value.search(e);
  } else {
    radioTabTemRef.value.search(e);
  }
};

// 删除/清空 ========================================================================================
const selectClear = (type: string) => {
  if (type === 'radio') {
    selectVal.value = [];
    selectOptions.value = [];
  } else {
    selectOptions.value = selectOptions.value.filter((i: any) =>
      selectVal.value.includes(i[props.infoVal.keyField]),
    );
  }

  submitVal.value = [];
  submitOptions.value = [];
  tableVal.value = [];
  tableSelect.value = [];

  emits('update:modelValue', selectVal.value);
};
</script>

<template>
  <div class="w-full" @click="selectOpen">
    <Select
      v-if="!props.isMultiple"
      class="w-full"
      v-model:value="selectVal"
      :options="selectOptions"
      :placeholder="`请选择${props.infoVal.selectTitle}`"
      :allow-clear="true"
      :open="false"
      @deselect="selectClear('radio')"
    />
    <Select
      v-else
      class="w-full"
      v-model:value="selectVal"
      :options="selectOptions"
      mode="multiple"
      :placeholder="`请选择${props.infoVal.selectTitle}`"
      :allow-clear="true"
      :max-tag-count="showMax"
      :show-arrow="true"
      :open="false"
      @deselect="selectClear('check')"
    />
  </div>

  <!-- ======================================================================================= -->
  <Modal :title="`选择${props.infoVal.selectTitle}`" class="w-1/2">
    <div v-if="props.infoVal.schema && props.infoVal.schema.length > 0">
      <searchTem :schema="props.infoVal.schema" @search="searchBtn" />
    </div>
    <Tabs v-model:active-key="activeKey">
      <TabPane
        :key="1"
        :tab="props.infoVal.selectTitle"
        v-if="
          props.infoVal.tabUrl &&
          columnsArr.length > 0 &&
          props.infoVal.keyField
        "
      >
        <radioTabTem
          v-if="!props.isMultiple"
          ref="radioTabTemRef"
          :api-url="props.infoVal.tabUrl"
          :columns="columnsArr"
          :key-field="props.infoVal.keyField"
          :check-row-keys="selectVal"
          :label-field-key="props.infoVal.labelFieldKey"
          :default-search="props.infoVal.defaultSearch"
          @radio-select="radioSelect"
        />
        <checkTabTem
          v-else
          ref="checkTabTemRef"
          :api-url="props.infoVal.tabUrl"
          :columns="columnsArr"
          :key-field="props.infoVal.keyField"
          :check-row-keys="selectVal"
          :label-field-key="props.infoVal.labelFieldKey"
          :default-search="props.infoVal.defaultSearch"
          @check-select="checkSelect"
        />
      </TabPane>
      <TabPane :key="2" :tab="`已选(${[...tableVal, ...submitVal].length})`">
        <Tabs
          v-model:active-key="activeKey2"
          tab-position="left"
          style="height: 500px"
        >
          <TabPane :key="1" tab="待提交">
            <div class="overflow-hidden">
              <div
                v-for="item in tableSelect"
                :key="item[props.infoVal.keyField]"
                class="float-left mb-3 mr-3"
              >
                <Button type="primary" @click="alreadyBtn(1, item)">
                  {{ item.label }}
                </Button>
              </div>
            </div>
          </TabPane>
          <TabPane :key="2" tab="已提交">
            <div class="overflow-hidden">
              <div
                v-for="item in selectOptions"
                :key="item[props.infoVal.keyField]"
                class="float-left mb-3 mr-3"
              >
                <Button
                  :type="
                    submitVal.includes(item[props.infoVal.keyField])
                      ? 'primary'
                      : 'default'
                  "
                  @click="alreadyBtn(0, item)"
                >
                  {{ item.label }}
                </Button>
              </div>
            </div>
          </TabPane>
        </Tabs>
      </TabPane>
    </Tabs>
  </Modal>
</template>

<style lang="scss" scoped></style>
