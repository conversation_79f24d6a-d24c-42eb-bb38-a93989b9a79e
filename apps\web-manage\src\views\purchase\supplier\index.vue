<template>
  <Page auto-content-height>
    <FormModel @success="onRefresh" />
    <Grid>
      <template #tableTags="{ row }">
        <Tag v-for="tag in row.tags.split(',')" :key="tag" color="blue">
          {{ tag }}
        </Tag>
      </template>
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          新增供应商
        </Button>
      </template>
    </Grid>
  </Page>
</template>

<script setup lang="ts">
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';
import { Button, message, Tag } from 'ant-design-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useColumns, useGridFormSchema } from './data';
import form from './modules/form.vue';
import { listSupplier, deleteSupplier, getInfo } from '#/api/purchase/supplier';
import { $t } from '#/locales';

const [FormModel, formModelApi] = useVbenModal({
  connectedComponent: form,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 2,
    wrapperClass: 'grid grid-cols-4 md:grid-cols-4 lg:grid-cols-5 gap-4',
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const res: any = await listSupplier({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

function onActionClick(e: OnActionClickParams<any>) {
  switch (e.code) {
    case 'delete': {
      onDelete(e.row);
      break;
    }
    case 'edit': {
      onEdit(e.row);
      break;
    }
  }
}

function onEdit(row: any) {
  formModelApi.setData(row).open();
}

function onDelete(row: any) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.name]),
    duration: 0,
    key: 'action_process_msg',
  });
  deleteSupplier({ id: row.id })
    .then(() => {
      message.success('删除成功');
      onRefresh();
      hideLoading();
    })
    .catch(() => {
      hideLoading();
    });
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formModelApi.setData({}).open();
}
</script>

<style scoped lang="scss"></style>
