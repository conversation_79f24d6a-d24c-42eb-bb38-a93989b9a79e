import { toRefs } from 'vue';
import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';

const { accessAllEnums } = toRefs(useAccessStore());
// 搜索表单
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '节假日名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入节假日名称',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'holidayType',
      label: '节假日类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择节假日类型',
        allowClear: true,
        options: accessAllEnums.value?.holidayType.list,
      },
    },
  ];
}

// 表单组件
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'holidayName',
      componentProps: {
        placeholder: '请输入节假日名称',
        allowClear: true,
      },
      label: '节假日名称',
      rules: 'required',
    },
    {
      component: 'Select',
      fieldName: 'holidayType',
      label: '节假日类型',
      rules: 'required',
      componentProps: {
        placeholder: '请选择节假日类型',
        allowClear: true,
        options: accessAllEnums.value?.holidayType.list.filter(
          (item: any) => item.value == 3,
        ),
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'dateRange',
      label: '日期范围',
      rules: 'required',
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['开始日期', '结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '不启用', value: 0 },
        ],
        optionType: 'default',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        allowClear: true,
      },
    },
  ];
}
// 表格组件
export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'holidayName',
      title: '节假日名称',
      width: 180,
    },
    {
      field: 'holidayType',
      title: '节假日类型',
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.holidayType.list.find(
          (item: any) => item.value === row.holidayType,
        )?.label;
      },
      width: 120,
    },
    {
      field: 'beginDate',
      title: '开始日期',
      width: 180,
    },
    {
      field: 'endDate',
      title: '结束日期',
      width: 180,
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 180,
    },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellTag',
      },
      width: 180,
    },
    {
      field: 'remark',
      title: '备注',
      align: 'left',
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'holidayName',
          nameTitle: '节假日名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'edit', // 默认的编辑按钮
            disabled: (row: any) => {
              return row.holidayType != 3;
            },
          },
          {
            code: 'delete', // 默认的删除按钮
            disabled: (row: any) => {
              return row.holidayType != 3;
            },
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 150,
    },
  ];
}
