<script setup lang="ts">
// 默认参数 =======================================================================================
import { ref } from 'vue';

import selectTem from '../components/select.vue';

// ================================================================================================

// 默认参数（必填项：tabUrl, labelFieldKey, keyField, columns）
const infoVal = ref({
  tabUrl: '/user/teacherGetPaging',
  labelFieldKey: 'label',
  keyField: 'value',
  selectTitle: '老师',
  defaultSearch: {
    // 默认搜索条件
    isInTheJob: true,
  },

  // 表格列
  columns: [
    { field: 'name', title: '老师名称' },
    { field: 'phone', title: '手机号码' },
    { field: 'nickName', title: '昵称' },
    { field: 'jobTypeDesc', title: '在职状态' },
    { field: 'roleName', title: '擅长科目' },
    { field: 'remark', title: '已上课时' },
    { field: 'remark', title: '已上课节' },
  ],

  // 搜索条件
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '姓名/手机号码',
      },
      fieldName: 'key',
    },
  ],
});
</script>

<template>
  <selectTem :info-val="infoVal" v-bind="$attrs" />
</template>

<style lang="scss" scoped></style>
