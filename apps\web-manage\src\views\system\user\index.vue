<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import type {
    OnActionClickParams,
    VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteUser, getAdminUserList, updateUserStatus, userPasswordReset } from '#/api';
import { $t } from '#/locales';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

const [FormModal, formModalApi] = useVbenModal({
    connectedComponent: Form,
    destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
    formOptions: {
        fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
        schema: useGridFormSchema(),
        collapsed: true,
        submitOnChange: true,
        submitOnEnter: true,
        collapsedRows: 1,
        wrapperClass: 'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
        showCollapseButton: true,
    },
    gridOptions: {
        columns: useColumns(onActionClick),
        height: 'auto',
        keepSource: true,
        headerCellConfig: {
            height: 40,
        },
        cellConfig: {
            height: 70,
        },
        proxyConfig: {
            ajax: {
                query: async ({ page }, formValues) => {
                    const res: any = await getAdminUserList({
                        page: page.currentPage,
                        pageSize: page.pageSize,
                        ...formValues,
                    });
                    return {
                        items: res.list,
                        total: res.total,
                    };
                },
            },
        },
        rowConfig: {
            keyField: 'id',
        },

        toolbarConfig: {
            custom: true,
            export: false,
            refresh: { code: 'query' },
            search: true,
            zoom: true,
        },
    } as VxeTableGridOptions<any>,
});

function onActionClick(e: OnActionClickParams<any>) {
    switch (e.code) {
        case 'delete': {
            onDelete(e.row);
            break;
        }
        case 'edit': {
            onEdit(e.row);
            break;
        }
        case 'resetPwd': {
            onResetPwd(e.row);
            break;
        }
        case 'enable': {
            onChangeStatus(1, e.row);
            break;
        }
        case 'disable': {
            onChangeStatus(-1, e.row);
            break;
        }
        case 'unlock': {
            onChangeStatus(1, e.row);
            break;
        }
    }
}

/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
function confirm(content: string, title: string) {
    return new Promise((reslove, reject) => {
        Modal.confirm({
            content,
            onCancel() {
                reject(new Error('已取消'));
            },
            onOk() {
                reslove(true);
            },
            title,
        });
    });
}

/**
 * 状态开关即将改变
 * @param newStatus 期望改变的状态值
 * @param row 行数据
 * @returns 返回false则中止改变，返回其他值（undefined、true）则允许改变
 */
async function onResetPwd(
    row: any,
) {
    try {
        await confirm(
            `你要将【${row.name}】的密码重置为初始密码吗？`,
            `重置密码`,
        );
        await userPasswordReset(row.id)
        message.success('重置密码成功！');
        return true;
    } catch {
        return false;
    }
}

/**
 * 
 * @param row 更改状态
 */

async function onChangeStatus(
    newStatus: any,
    row: any,
) {
    const status: any = {
        1: '启用',
        '-1': '禁用',
        '-2': '锁定',
    };
    try {
        await confirm(
            `你要将用户【${row.name}】的状态切换为 【${status[newStatus.toString()]}】 吗？`,
            `切换状态`,
        );
        await updateUserStatus({ id: row.id, status: newStatus });
        message.success(`用户【${row.name}】已切换为【${status[newStatus.toString()]}】`);
        onRefresh();
        return true;
    } catch {
        return false;
    }
}

function onEdit(row: any) {
    formModalApi.setData(row).open();
}

function onDelete(row: any) {
    const hideLoading = message.loading({
        content: $t('ui.actionMessage.deleting', [row.name]),
        duration: 0,
        key: 'action_process_msg',
    });
    deleteUser(row.id)
        .then(() => {
            message.success({
                content: $t('ui.actionMessage.deleteSuccess', [row.name]),
                key: 'action_process_msg',
            });
            onRefresh();
        })
        .catch(() => {
            hideLoading();
        });
}

function onRefresh() {
    gridApi.query();
}

function onCreate() {
    formModalApi.setData({}).open();
}
</script>
<template>
    <Page auto-content-height>
        <FormModal @success="onRefresh" />
        <Grid>
            <template #toolbar-actions>
                <Button type="primary" @click="onCreate">
                    <Plus class="size-5" />
                    新增用户
                </Button>
            </template>
            <template #roleRender="{ row }">
                <div class="flex items-center justify-center gap-1 flex-wrap">
                    <Tag color="default" v-for="tag in row.roles">{{ tag.name }}</Tag>
                </div>
            </template>
        </Grid>
    </Page>
</template>
