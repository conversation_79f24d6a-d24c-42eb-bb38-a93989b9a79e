import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { getAllScenicList } from '#/api';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '设备名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入设备名称',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'deviceType',
      label: '闸机类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择闸机类型',
        allowClear: true,
        options: accessAllEnums.value?.gateDeviceType.list,
      },
    },
    {
      component: 'Input',
      fieldName: 'deviceSn',
      label: '设备SN',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入设备SN',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: accessAllEnums.value?.status.list,
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'deviceName',
      title: '闸机名称',
      width: 200,
      fixed: 'left',
    },
    {
      field: 'deviceType',
      title: '闸机类型',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.gateDeviceType.list.find(
          (item: any) => item.value === row.deviceType,
        )?.label;
      },
    },
    {
      field: 'scenicName',
      title: '所属景区',
      width: 150,
      formatter: ({ row }: any) => {
        return row.scenicInfo.scenicName;
      },
    },
    {
      field: 'deviceMac',
      title: 'MAC地址',
      width: 150,
    },
    {
      field: 'deviceSn',
      title: '设备SN',
      width: 200,
    },
    {
      field: 'ip',
      title: 'IP地址',
      width: 150,
    },
    {
      field: 'method',
      title: '开门状态',
      width: 100,
      formatter: ({ row }: any) => {
        return row.method === 1 ? '进' : '出';
      },
    },
    {
      field: 'status',
      width: 120,
      title: '状态',
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: onStatusChange,
        },
      },
    },
    {
      field: 'deviceModel',
      title: '设备型号',
      width: 150,
    },
    {
      field: 'manufacturer',
      title: '制造商',
      width: 150,
    },
    {
      field: 'address',
      title: '安装位置',
      width: 150,
    },
    {
      field: 'createdBy',
      title: '创建人',
      width: 150,
    },
    {
      field: 'operation',
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'deviceName',
          nameTitle: '闸机',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'log',
            text: '核销记录',
          },
          'edit',
          'delete',
        ],
      },
      fixed: 'right',
      title: '操作',
      width: 180,
    },
  ];
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'deviceName',
      label: '设备名称',
      componentProps: {
        placeholder: '请输入设备名称',
        allowClear: true,
      },
      rules: 'required',
    },
    {
      component: 'Select',
      fieldName: 'deviceType',
      label: '闸机类型',
      componentProps: {
        placeholder: '请选择闸机类型',
        allowClear: true,
        options: accessAllEnums.value?.gateDeviceType.list,
      },
      rules: 'selectRequired',
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'method',
      label: '开门方式',
      componentProps: {
        placeholder: '请选择开门方式',
        allowClear: true,
        options: [
          { label: '进', value: 1 },
          { label: '出', value: 2 },
        ],
      },
      defaultValue: 1,
      rules: 'selectRequired',
    },
    {
      component: 'Input',
      fieldName: 'deviceSn',
      label: '设备序列号',
      componentProps: {
        placeholder: '请输入设备序列号',
        allowClear: true,
      },
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'deviceMac',
      label: 'MAC地址',
      componentProps: {
        placeholder: '请输入MAC地址',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'ip',
      label: 'IP地址',
      componentProps: {
        placeholder: '请输入IP地址',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'deviceModel',
      label: '设备型号',
      componentProps: {
        placeholder: '请输入设备型号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'manufacturer',
      label: '设备制造商',
      componentProps: {
        placeholder: '请输入设备制造商',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'address',
      label: '安装位置',
      componentProps: {
        placeholder: '请输入安装位置',
        allowClear: true,
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        allowClear: true,
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '不启用', value: 0 },
        ],
        optionType: 'default',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
      rules: 'selectRequired',
    },
  ];
}

export function useGridFormLogSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'ticketName',
      label: '门票名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入门票名称',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'verificationMode',
      label: '核销方式',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择核销方式',
        allowClear: true,
        options: accessAllEnums.value?.tickeVerificationMode.list,
      },
    },
    {
      component: 'Input',
      fieldName: 'verificationCode',
      label: '核销码',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入核销码',
        allowClear: true,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'verificationDate',
      label: '开卡时间',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['核销开始日期', '核销结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}
export function useLogColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'ticketName',
      title: '门票名称',
      width: 150,
    },
    {
      field: 'childTicketName',
      title: '子票名称',
      width: 150,
    },
    {
      field: 'scenicName',
      title: '核销景区',
      width: 150,
      formatter: ({ row }: any) => row.scenicInfo.scenicName || '--',
    },
    {
      field: 'verificationCode',
      title: '核销码',
      width: 150,
    },
    {
      field: 'verificationTime',
      title: '核销时间',
      width: 150,
    },
    {
      field: 'verificationMode',
      title: '核销方式',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.tickeVerificationMode.list.find(
          (item: any) => item.value === row.verificationMode,
        )?.label;
      },
    },
    {
      field: 'verificationType',
      title: '核销类型',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.tickeVerificationType.list.find(
          (item: any) => item.value === row.verificationType,
        )?.label;
      },
    },
    {
      field: 'verificationNum',
      title: '核销数量',
      width: 100,
    },
    {
      field: 'remark',
      title: '备注',
      width: 150,
    },
  ];
}
