import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { getAllUserList } from '#/api';
import dayjs from 'dayjs';
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'ip',
      label: '登录IP',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入登录IP',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'type',
      label: '类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择类型',
        allowClear: true,
        options: [
          {
            label: '手机',
            value: 1,
          },
          {
            label: 'PC',
            value: 2,
          },
        ],
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: [
          {
            label: '成功',
            value: 1,
          },
          {
            label: '失败',
            value: 2,
          },
        ],
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '用户',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.name,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllUserList,
        placeholder: '请选择用户',
        allowClear: true,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'date',
      label: '日期',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['开始日期', '结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
      defaultValue: [dayjs().startOf('month'), dayjs().endOf('month')], // 默认当月
    },
  ];
}

export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '用户',
      field: 'name',
      width: 120,
      formatter: ({ row }: any) => {
        return row.userInfo.name || '--';
      },
    },
    {
      title: '手机号',
      field: 'phone',
      width: 120,
      formatter: ({ row }: any) => {
        return row.userInfo.phone || '--';
      },
    },
    {
      title: '登录IP',
      field: 'ip',
      width: 120,
    },
    {
      title: 'IP位置',
      field: 'region',
      width: 200,
    },
    {
      title: 'userAgent信息',
      field: 'userAgent',
      minWidth: 200,
      slots: { default: 'userAgent' },
      align: 'left',
    },
    {
      title: '登录方式',
      field: 'loginType',
      width: 120,
      formatter: ({ row }: any) => {
        return row.loginType === 1 ? '密码登录' : '短信验证码登录';
      },
    },
    {
      title: '登录客户端',
      field: 'client',
      width: 120,
      formatter: ({ row }: any) => {
        return row.client === 1 ? 'PC' : '手机';
      },
    },
    {
      title: '状态',
      field: 'status',
      width: 120,
      cellRender: {
        name: 'CellTag',
        options: [
          { color: 'success', label: '成功', value: 1 },
          { color: 'error', label: '失败', value: 2 },
        ],
      },
    },
    {
      title: '登录时间',
      field: 'createdAt',
      width: 150,
    },
  ];
}
