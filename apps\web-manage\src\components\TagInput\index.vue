<!-- 封装一个可填写Tag标签的input组件 -->
<template>
  <div class="tag-input-wrapper">
    <div class="tag-list">
      <Tag
        v-for="(tag, index) in tags"
        :key="index"
        :color="tagColor"
        class="me-0"
        closable
        @close="handleClose(index)"
      >
        {{ tag }}
      </Tag>
      <Input
        v-if="!isMaxTags"
        v-model:value="inputValue"
        :placeholder="placeholder +'，最多添加'+ maxTags +'个标签'"
        :disabled="disabled"
        class="tag-input"
        @keydown="handleKeydown"
        @blur="handleInputConfirm"
      />
      <div v-if="showCount" class="tag-count">
        {{ tags.length }}/{{ maxTags }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Input, Tag } from 'ant-design-vue';

interface Props {
  modelValue?: string[] | string;
  placeholder?: string;
  maxTags?: number;
  disabled?: boolean;
  tagColor?: string;
  showCount?: boolean;
  separator?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  placeholder: '请输入标签',
  maxTags: 10,
  disabled: false,
  tagColor: 'blue',
  showCount: true,
  separator: ',',
});

const emit = defineEmits(['update:modelValue', 'change']);

// 将输入值转换为标签数组
const convertToTags = (value: string | string[]): string[] => {
  if (Array.isArray(value)) {
    return value;
  }
  if (typeof value === 'string') {
    return value
      .split(props.separator)
      .map((tag) => tag.trim())
      .filter(Boolean);
  }
  return [];
};

// 将标签数组转换为输出值
const convertToOutput = (tags: string[]): string[] | string => {
  if (Array.isArray(props.modelValue)) {
    return tags;
  }
  return tags.join(props.separator);
};

const tags = ref<string[]>(convertToTags(props.modelValue));
const inputValue = ref('');

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    tags.value = convertToTags(newValue);
  },
);

// 是否达到最大标签数
const isMaxTags = computed(() => tags.value.length >= props.maxTags);

// 处理输入确认
const handleInputConfirm = () => {
  if (inputValue.value && !isMaxTags.value) {
    // 处理可能的逗号分隔输入
    const newTags = inputValue.value
      .split(props.separator)
      .map((tag) => tag.trim())
      .filter(Boolean);

    // 添加新标签（过滤掉重复的）
    newTags.forEach((tag) => {
      if (!tags.value.includes(tag)) {
        tags.value.push(tag);
      }
    });

    // 如果超过最大标签数，截取前面的标签
    if (tags.value.length > props.maxTags) {
      tags.value = tags.value.slice(0, props.maxTags);
    }

    emit('update:modelValue', convertToOutput(tags.value));
    emit('change', convertToOutput(tags.value));
  }
  inputValue.value = '';
};

// 处理标签删除
const handleClose = (index: number) => {
  tags.value.splice(index, 1);
  emit('update:modelValue', convertToOutput(tags.value));
  emit('change', convertToOutput(tags.value));
};

// 处理退格键删除
const handleKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    e.preventDefault();
    handleInputConfirm();
  } else if (e.key === 'Backspace') {
    if (!inputValue.value && tags.value.length > 0) {
      tags.value.pop();
      emit('update:modelValue', convertToOutput(tags.value));
      emit('change', convertToOutput(tags.value));
    }
  }
};
</script>

<style scoped>
.tag-input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tag-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  padding: 4px 11px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  min-height: 32px;
}

.tag-list:hover {
  border-color: #278df2;
}

.tag-input {
  width: auto;
  min-width: 60px;
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
  background: transparent;
  flex: 1;
}

.tag-input:focus {
  box-shadow: none;
}

.tag-count {
  font-size: 12px;
  color: #999;
  text-align: right;
}
</style>
