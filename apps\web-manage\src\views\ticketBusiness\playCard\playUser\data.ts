import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs, markRaw } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'cardNo',
      label: '卡号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入卡号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'cardName',
      label: '卡名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入卡名称',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'cardStatus',
      label: '卡状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择卡状态',
        allowClear: true,
        options: accessAllEnums.value.ticketCardStatus.list,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'orderDate',
      label: '购买日期',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['购买开始日期', '购买结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'activeDate',
      label: '激活日期',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['激活开始日期', '激活结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}

export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'cardNo',
      title: '卡号',
      width: 200,
    },
    {
      field: 'cardName',
      title: '游玩卡名称',
      minWidth: 200,
    },
    {
      field: 'cardInfo',
      title: '持卡人信息',
      children: [
        {
          field: 'cardUserName',
          title: '持卡人姓名',
          width: 150,
        },
        {
          field: 'cardUserPhone',
          title: '持卡人手机号',
          width: 150,
        },
        {
          field: 'cardUserIdCard',
          title: '持卡人身份证号',
          width: 150,
        },
      ],
    },
    {
      field: 'isBindFace',
      title: '绑定人脸',
      slots: { default: 'isBindFace' },
      width: 150,
    },
    {
      field: 'cardStatus',
      title: '卡状态',
      width: 150,
      cellRender: {
        name: 'CellTag',
        options: accessAllEnums.value?.ticketCardStatus.list.map(
          (item: any) => ({
            label: item.label,
            value: item.value,
            color: type[item.value],
          }),
        ),
      },
      formatter: ({ row }) => {
        return accessAllEnums.value?.ticketCardStatus.list.find(
          (item: any) => item.value === row.cardStatus,
        )?.label;
      },
    },
    {
      field: 'orderTime',
      title: '购买时间',
      width: 150,
    },
    {
      field: 'activateTime',
      title: '激活时间',
      width: 150,
    },
    {
      field: 'validDate',
      title: '有效时间',
      width: 160,
      slots: { default: 'validDate' },
    },
    {
      field: 'canVerificationNum',
      title: '可核销次数',
      width: 150,
      slots: { default: 'canVerificationNum' },
    },
    {
      field: 'verificationTotal',
      title: '核销总数',
      width: 150,
      slots: { default: 'verificationTotal' },
    },
    {
      field: 'operation',
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: '操作',
      width: 120,
    },
  ];
}
const type: any = {
  7: 'default',
  8: 'warning',
  9: 'success',
  11: 'error',
  12: 'default',
};
