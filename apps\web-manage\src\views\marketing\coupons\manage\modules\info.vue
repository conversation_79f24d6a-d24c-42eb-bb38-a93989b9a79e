<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Tag } from 'ant-design-vue';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

const couponInfo = ref<any>({});
const [Model, modelApi] = useVbenModal({
  title: '优惠券详情',
  showCancelButton: false,
  confirmText: '关闭',
  async onConfirm() {
    modelApi.close();
  },
  onOpenChange(isOpen) {
    let data = modelApi.getData();
    if (isOpen) {
      console.log(data, 'data');
      couponInfo.value = data;
    }
  },
});

const filterStatus = (val: any) => {
  return accessAllEnums.value?.couponStatus.list.find(
    (item: any) => item.value === val,
  )?.label;
};
</script>
<template>
  <Model class="w-[550px]">
    <div class="flex items-end">
      <Tag :color="couponInfo.couponType === 1 ? 'warning' : 'error'">{{
        couponInfo.couponType === 1 ? '满减券' : '折扣券'
      }}</Tag>
      <div :class="`ml-2 text-center text-base text-color-${couponInfo.couponType}`">
        <span v-if="couponInfo.couponType === 1">￥</span>
        <span class="mx-1 text-5xl font-medium">
          {{ couponInfo.couponValue }}
        </span>
        <span v-if="couponInfo.couponType === 2">折</span>
      </div>
    </div>
    <div class="mt-4">
      <div class="text-lg">
        {{ couponInfo.couponName }}【{{ filterStatus(couponInfo.status) }}】
      </div>
      <div class="mt-1 leading-6 text-sm">
        <div>
          有效期：{{
            couponInfo.validType == 1
              ? '永久有效'
              : couponInfo.validType == 2
                ? `${couponInfo.validBeginDate}~${couponInfo.validEndDate}`
                : `领取后${couponInfo.validDayNum}天有效`
          }}
        </div>
        <div>
          使用门槛：{{
            couponInfo.thresholdType == 1
              ? '无门槛'
              : '最低消费' + couponInfo.thresholdPrice + '元'
          }}
        </div>
      </div>
    </div>

    <div class="text-sm mt-3 leading-6">
      <div class="flex gap-5">
        <span><span class="opacity-50">库存数量：</span>{{ couponInfo.stockNum }}</span>
        <span><span class="opacity-50">剩余数量：</span>{{ couponInfo.availableStockNum }}</span>
      </div>
      <div class="flex gap-5">
        <div><span class="opacity-50">单人单日限领：</span>{{ couponInfo.dayLimit }}</div>
        <div><span class="opacity-50">单人限领：</span>{{ couponInfo.userLimit }}</div>
      </div>
      <div v-if="couponInfo.getTimeType === 1"><span class="opacity-50">在线领券截止日期：</span>不限日期</div>
      <div v-if="couponInfo.getTimeType === 2">
        <span class="opacity-50">在线领券截止日期：</span>{{ couponInfo.getTimeBeginDate }}~{{ couponInfo.getTimeEndDate }}
      </div>
      <div v-if="couponInfo.scenicScope">
        <span class="opacity-50">适用景区范围：</span>
        <Tag type="default" v-for="item in couponInfo.scenicScopeList" :key="item.id">{{ item.scenicName }}</Tag>
      </div>
      <div v-if="couponInfo.ticketScope">
        <span class="opacity-50">适用门票范围：</span>
        <Tag type="default" v-for="item in couponInfo.ticketScopeList" :key="item.id">{{ item.ticketName }}</Tag>
      </div>
      <div><span class="opacity-50">备注：</span>{{ couponInfo.remark }}</div>
      <div><span class="opacity-50">使用说明：</span>
        <p v-html="couponInfo.couponDesc"></p>
      </div>
    </div>
  </Model>
</template>
