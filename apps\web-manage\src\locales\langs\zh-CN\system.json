{"dept": {"list": "部门列表", "createTime": "创建时间", "deptName": "部门名称", "name": "部门", "operation": "操作", "parentDept": "上级部门", "remark": "备注", "status": "状态", "title": "部门管理"}, "menu": {"list": "菜单列表", "activeIcon": "激活图标", "activePath": "激活路径", "activePathHelp": "跳转到当前路由时，需要激活的菜单路径。\n当不在导航菜单中显示时，需要指定激活路径", "activePathMustExist": "该路径未能找到有效的菜单", "advancedSettings": "其它设置", "affixTab": "固定在标签", "authCode": "权限标识", "badge": "徽章内容", "badgeVariants": "徽标样式", "badgeType": {"dot": "点", "none": "无", "normal": "文字", "title": "徽标类型"}, "component": "页面组件", "hideChildrenInMenu": "隐藏子菜单", "hideInBreadcrumb": "在面包屑中隐藏", "hideInMenu": "隐藏菜单", "hideInTab": "在标签栏中隐藏", "icon": "图标", "keepAlive": "缓存标签页", "linkSrc": "链接地址", "menuName": "菜单名称", "menuTitle": "标题", "name": "菜单", "operation": "操作", "parent": "上级菜单", "path": "路由地址", "status": "状态", "title": "菜单管理", "type": "类型", "typeButton": "按钮", "typeCatalog": "目录", "typeEmbedded": "内嵌", "typeLink": "外链", "typeMenu": "菜单", "method": "请求方式", "apiPath": "请求接口", "listorder": "排序", "query": "页面参数", "openInNewWindow": "在新窗口打开", "noBasicLayout": "无需基础布局"}, "role": {"title": "角色管理", "list": "角色列表", "name": "角色", "roleName": "角色名称", "id": "角色ID", "status": "状态", "remark": "备注", "createTime": "创建时间", "operation": "操作", "permissions": "权限", "setPermissions": "授权"}, "title": "系统管理"}