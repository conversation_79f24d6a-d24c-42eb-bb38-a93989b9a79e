<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { computed, ref, watch } from 'vue';
import { useVbenModal, ApiComponent } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { useFormSchema } from '../data';
import { createTicketOffice, updateTicketOffice } from '#/api';
import { Button, message, Select } from 'ant-design-vue';
import SelectTicket from './selectTicket/index.vue';
import SelectUser from './selectUser/index.vue';
const emits = defineEmits(['success']);

const formData = ref<Recordable<any>>({});
// 表单配置
const [Form, formApi] = useVbenForm({
    scrollToFirstError: true,
    schema: useFormSchema(),
    showDefaultActions: false,
    // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2',
    commonConfig: {
        // 所有表单项
        labelWidth: 120,
        componentProps: {
            class: 'w-full',
        },
    },
});
// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
    async onConfirm() {
        await handleSubmit();
    },
    onOpenChange(isOpen) {
        const data = modelApi.getData<any>();
        formApi.resetForm();
        if (isOpen) {
            if (data) {
                // 创建新对象而不是直接赋值
                const newFormData = { ...data };
                newFormData.ticketIds = data.ticketList
                newFormData.userIds = data.userList
                // 赋值给formData.value
                formData.value = newFormData;
                id.value = data.id;
                formApi.setValues(formData.value);
            }
        }
    },
});

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
    const { valid } = await formApi.validate();
    if (!valid) return null;

    const values = await formApi.getValues();
    values.ticketIds = values.ticketIds.map((item: any) => item.id);
    values.userIds = values.userIds.map((item: any) => item.id);

    return values;
};

// 提交表单
const handleSubmit = async () => {
    const values = await processFormValues();
    if (!values) return;
    modelApi.lock();
    try {
        if (formData.value.id) {
            await updateTicketOffice({ id: id.value, ...values });
        } else {
            await createTicketOffice(values);
        }
        emits('success');
        modelApi.close();
    } catch (error) {
        modelApi.unlock();
    }
};

const selectTicketRef = ref<any>(null);
const scenicIds = ref<any>(null);
const addTicketBtn = async () => {
    const values = await formApi.getValues();
    scenicIds.value = values.scenicId;
    if (!scenicIds.value) {
        message.warning('请先选择所属景区！');
        return;
    }
    selectTicketRef.value[0].addTicket(scenicIds.value);
};


// 获取表单标题
const getModelTitle = computed(() => {
    return formData.value.id ? '编辑售票点' : '新增售票点';
});
</script>
<template>
    <Model class="w-[50%]" :title="getModelTitle">
        <Form>
            <template #ticketIds="slotProps">
                <div class="w-full">
                    <Button type="primary" class="mb-2" @click="addTicketBtn">添加门票</Button>
                    <SelectTicket v-bind="slotProps" ref="selectTicketRef"></SelectTicket>
                </div>
            </template>
            <template #userIds="slotProps">
                <SelectUser v-bind="slotProps"></SelectUser>
            </template>
            <template #ticketMachineIds="slotProps">
                <ApiComponent :component="Select" v-bind="slotProps" ref="childTicketRef">
                    <template #option="{ value: id, label, sn }">
                        <div class="flex items-center justify-between" :aria-label="id">
                            <span>{{ label }}</span>
                            <span class="text-xs text-gray-500">{{ sn }}</span>
                        </div>
                    </template>
                </ApiComponent>
            </template>
        </Form>
    </Model>
</template>
