<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Descriptions, DescriptionsItem, Image } from 'ant-design-vue';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

const cardInfo = ref<any>({});
const [Model, modelApi] = useVbenModal({
  title: '游玩卡详情',
  showCancelButton: false,
  confirmText: '关闭',
  async onConfirm() {
    modelApi.close();
  },
  onOpenChange(isOpen) {
    let data = modelApi.getData();
    if (isOpen) {
      cardInfo.value = data;
    }
  },
});

const filterStatus = (val: any) => {
  return accessAllEnums.value?.ticketCardStatus.list.find(
    (item: any) => item.value === val,
  )?.label;
};
</script>
<template>
  <Model class="w-[50%]">
    <Descriptions title="" bordered :column="2">
      <DescriptionsItem label="卡号">{{ cardInfo.cardNo }}</DescriptionsItem>
      <DescriptionsItem label="游玩卡名称">{{
        cardInfo.cardName
      }}</DescriptionsItem>
      <DescriptionsItem label="游玩卡状态">{{
        filterStatus(cardInfo.cardStatus)
      }}</DescriptionsItem>
      <DescriptionsItem label="持卡人">{{
        cardInfo.cardUserName
      }}</DescriptionsItem>
      <DescriptionsItem label="持卡人手机号">{{
        cardInfo.cardUserPhone
      }}</DescriptionsItem>
      <DescriptionsItem label="持卡人身份证">{{
        cardInfo.cardUserIdCard
      }}</DescriptionsItem>
      <DescriptionsItem label="人脸信息" :span="2">
        <Image
          v-if="cardInfo.faceImg"
          :src="cardInfo.faceImg"
          style="width: 80px; height: 80px"
        />
      </DescriptionsItem>
      <DescriptionsItem label="购买时间">{{
        cardInfo.orderTime
      }}</DescriptionsItem>
      <DescriptionsItem label="有效期">{{
        cardInfo.validBeginDate + '~' + cardInfo.validEndDate
      }}</DescriptionsItem>
    </Descriptions>
  </Model>
</template>
