<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { computed, ref, h, watch, defineProps, defineEmits } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Input, Select, Button, Table, Form, FormItem } from 'ant-design-vue';
import { getTicketList, getTicketTypeAllList } from '#/api';

const emits = defineEmits(['change']);
const props = defineProps({
  selectedTickets: {
    type: Array,
    default: () => [],
  },
  scenicId: {
    type: [String, Number],
    default: undefined,
  },
});

const [Model, modelApi] = useVbenModal({
  title: '门票列表',
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      setTimeout(() => {
        getTicketLists();
        getTypeList();
      }, 0);
    }
  },
});
const columns = ref([
  {
    title: '门票名称',
    dataIndex: 'ticketName',
  },
  {
    title: '门票分类',
    dataIndex: 'ticketType',
  },
  {
    title: '销售价',
    dataIndex: 'sellingPrice',
  },
]);
const params = ref({
  name: '',
  page: 1,
  pageSize: 10,
  model: 1,
  typeId: undefined,
  scenicId: props.scenicId,
});

watch(
  () => props.scenicId,
  (val) => {
    params.value.scenicId = val;
  },
  { immediate: true, deep: true },
);

const ticketList = ref<any[]>([]);
const typeList = ref<any[]>([]);
const total = ref(0);
const getTicketLists = async () => {
  const res = await getTicketList(params.value);
  ticketList.value = res.list;
  total.value = res.total;
};
const resetForm = () => {
  params.value = {
    name: '',
    page: 1,
    pageSize: 10,
    model: 1,
    typeId: undefined,
    scenicId: props.scenicId,
  };
  getTicketLists();
};
const handleSearch = () => {
  params.value.page = 1;
  getTicketLists();
};

const getTypeList = async () => {
  const res = await getTicketTypeAllList({});
  typeList.value = res.map((item: any) => {
    return { label: item.typeName, value: item.id };
  });
};

const selectedRowKeys = ref<any[]>([]);
const selectedRows = ref<any[]>([]);

watch(
  () => props.selectedTickets,
  (val) => {
    if (val && val.length > 0) {
      console.log(val, 'selectedTickets');
      selectedRows.value = val;
      selectedRowKeys.value = val.map((item: any) => item.id);
    } else {
      selectedRows.value = [];
      selectedRowKeys.value = [];
    }
  },
  { immediate: true, deep: true },
);

const rowSelection = computed(() => ({
  checkStrictly: false,
  selectedRowKeys: selectedRowKeys.value.filter(key => ticketList.value.some(item => item.id === key)), // 仅显示当前页面的选定按键
  onChange: (
    currentSelectedKeys: (string | number)[],
    currentSelectedRows: any[],
  ) => {
    const newGlobalSelectedKeys = new Set(selectedRowKeys.value);
    const newGlobalSelectedRows = [...selectedRows.value];

    const currentPageRowIds = new Set(ticketList.value.map(item => item.id));

    // 1. Handle deselections:
    const deselectedOnCurrentPage = Array.from(newGlobalSelectedKeys).filter(key =>
      currentPageRowIds.has(key) && !currentSelectedKeys.includes(key)
    );
    deselectedOnCurrentPage.forEach(key => {
      newGlobalSelectedKeys.delete(key);
      const index = newGlobalSelectedRows.findIndex(row => row.id === key);
      if (index !== -1) {
        newGlobalSelectedRows.splice(index, 1);
      }
    });

    // 2. Handle selections:
    currentSelectedRows.forEach(row => {
      if (!newGlobalSelectedKeys.has(row.id)) {
        newGlobalSelectedKeys.add(row.id);
        newGlobalSelectedRows.push(row);
      }
    });

    selectedRowKeys.value = Array.from(newGlobalSelectedKeys);
    selectedRows.value = newGlobalSelectedRows;
  },
  onSelect: (record: any, selected: boolean) => {
    if (selected) {
      if (!selectedRows.value.some((item) => item.id === record.id)) {
        selectedRows.value.push(record);
        selectedRowKeys.value.push(record.id);
      }
    } else {
      selectedRows.value = selectedRows.value.filter(
        (item) => item.id !== record.id,
      );
      selectedRowKeys.value = selectedRowKeys.value.filter(
        (key) => key !== record.id,
      );
    }
  },
  onSelectAll: (
    selected: boolean,
    currentSelectedRows: any[],
    changeRows: any[],
  ) => {
    if (selected) {
      changeRows.forEach((row) => {
        if (!selectedRows.value.some((item) => item.id === row.id)) {
          selectedRows.value.push(row);
          selectedRowKeys.value.push(row.id);
        }
      });
    } else {
      const changeRowIds = new Set(changeRows.map((row) => row.id));
      selectedRows.value = selectedRows.value.filter(
        (item) => !changeRowIds.has(item.id),
      );
      selectedRowKeys.value = selectedRowKeys.value.filter(
        (key) => !changeRowIds.has(key),
      );
    }
  },
}));

const handleSubmit = () => {
  emits(
    'change',
    selectedRows.value.map((item) => {
      return {
        ...item,
        childTicketId: item.id,
        isLimit: 0,
      };
    }),
  );
  modelApi.close();
};
</script>
<template>
  <Model class="w-[50%]" title="门票列表">
    <Form layout="inline" :model="params" class="mb-2">
      <FormItem label="门票名称">
        <Input v-model:value="params.name" placeholder="请输入门票名称" />
      </FormItem>
      <FormItem label="门票分类">
        <Select
          v-model:value="params.typeId"
          :options="typeList"
          placeholder="请选择门票分类"
        />
      </FormItem>
      <FormItem>
        <Button @click="resetForm()">重置</Button>
        <Button type="primary" class="ml-2" @click="handleSearch()"
          >搜索</Button
        >
      </FormItem>
    </Form>
    <div>
      <Table
        :rowSelection="rowSelection"
        :columns="columns"
        :dataSource="ticketList"
        rowKey="id"
        :pagination="{
          current: params.page,
          pageSize: params.pageSize,
          total: total,
          onChange: (page, pageSize) => {
            params.page = page;
            params.pageSize = pageSize;
            getTicketLists();
          },
          showTotal: (total) => `共 ${total} 条`,
        }"
        bordered
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'ticketType'">
            {{ record.ticketTypeInfo.typeName }}
          </template>
        </template>
      </Table>
    </div>
  </Model>
</template>
