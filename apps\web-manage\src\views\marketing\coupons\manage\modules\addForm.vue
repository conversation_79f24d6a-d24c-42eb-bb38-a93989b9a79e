<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { useAddFormSchema } from '../data';
import { createCoupon } from '#/api';
import { Button, message } from 'ant-design-vue';
import SelectTicket from './selectTicket/index.vue'

const emit = defineEmits(['success']);
const formData = ref<any>({});
// 表单配置
const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    labelWidth: 120,
    componentProps: {
      class: 'w-full',
    },
  },
  fieldMappingTime: [['couponTypes', ['couponType', 'couponValue'], null],
  ['validTypes', ['validType', 'validData'], null],
  ['getTimeTypes', ['getTimeType', 'getTimeDate'], null],
  ['thresholdTypes', ['thresholdType', 'thresholdPrice'], null]],
  scrollToFirstError: true,
  schema: useAddFormSchema(),
  showDefaultActions: false,
  // 一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2',
});
// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    const data = modelApi.getData<any>();
    formApi.resetForm();
    if (isOpen) {
      if (data) {
        // 创建新对象而不是直接赋值
        const newFormData = { ...data };
        newFormData.couponTypes = [1, null];
        newFormData.validTypes = [1, null];
        newFormData.getTimeTypes = [1, null];
        newFormData.thresholdTypes = [1, null];
        // 赋值给formData.value
        formData.value = newFormData;
        id.value = data.id;
        formApi.setValues(formData.value);
      }
    }
  },
});

const selectTicketRef = ref<any>(null);
const scenicIds = ref<any>(null);
const addTicketBtn = async () => {
  const values = await formApi.getValues();
  scenicIds.value = values.scenicScope;
  if (!scenicIds.value || scenicIds.value.length == 0) {
    message.warning('请先选择适用景区！');
    return;
  }
  selectTicketRef.value[0].addTicket(scenicIds.value);
};

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();
  if (values.validType == 2) {
    values.validBeginDate = values.validData[0];
    values.validEndDate = values.validData[1];
  } else {
    values.validDayNum = values.validData
  }
  if (values.getTimeType == 2) {
    values.getTimeBeginDate = values.getTimeDate[0];
    values.getTimeEndDate = values.getTimeDate[1];
  }
  if (values.ticketScope && values.ticketScope.length) {
    values.ticketScope = values.ticketScope.map((item: any) => item.id);
  }

  return values;
};

// 统一的提交处理函数
const handleSubmit = async () => {
  const values = await processFormValues();
  console.log(values, 'submit');
  if (!values) return;
  modelApi.lock();
  try {
    await createCoupon({ ...values });
    message.success('操作成功');
    emit('success');
    modelApi.close();
  } catch (error) {
    modelApi.unlock();
  }
};
</script>
<template>
  <Model class="w-[70%]" title="新增优惠券">
    <Form>
      <template #ticketScope="slotProps">
        <div class="w-full">
          <Button type="primary" class="mb-2" @click="addTicketBtn">添加门票</Button>
          <SelectTicket v-bind="slotProps" ref="selectTicketRef"></SelectTicket>
        </div>
      </template>
    </Form>
  </Model>
</template>
