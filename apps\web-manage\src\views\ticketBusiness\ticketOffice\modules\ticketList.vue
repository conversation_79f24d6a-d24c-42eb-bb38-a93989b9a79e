<script setup lang="ts">
import { ref, toRefs } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useAccessStore } from '@vben/stores';
import { Table } from 'ant-design-vue';
const { accessAllEnums } = toRefs(useAccessStore());

const ticketList = ref<any>({});
const columns = ref([
    {
        title: '门票名称',
        dataIndex: 'ticketName',
    },
    {
        title: '门票类型',
        dataIndex: 'model',
    },
    {
        title: '销售价',
        dataIndex: 'sellingPrice',
    },
]);
const [Model, modelApi] = useVbenModal({
    title: '可售门票列表',
    showCancelButton: false,
    confirmText: '关闭',
    async onConfirm() {
        modelApi.close();
    },
    onOpenChange(isOpen) {
        let data = modelApi.getData();
        if (isOpen) {
            ticketList.value = data;
        }
    },
});
const filterModel = (val: any) => {
    return accessAllEnums.value.ticketModel.list.find(
        (item: any) => item.value === val,
    )?.label;
}
</script>
<template>
    <Model class="w-[50%]">
        <Table :columns="columns" :dataSource="ticketList" rowKey="id" bordered :pagination="false">
            <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'model'">
                    {{ filterModel(record.model) }}
                </template>
            </template>
        </Table> 
    </Model>
</template>
