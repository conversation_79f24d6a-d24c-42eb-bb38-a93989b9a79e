<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { useRefundFormSchema } from '../data';
import { orderFullRefund, orderInitiativeRefund, orderPartRefund } from '#/api';
import { message } from 'ant-design-vue';

const emits = defineEmits(['success']);
const typeMap: any = {
  all: '全部退款',
  part: '部分退款',
  initiative: '主动退款',
};
const title = ref('全部退款');
const type = ref('all');
const orderData = ref<any>({});
const handleSubmint = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();
  console.log(values);
  if (type.value === 'all') {
    handleAllRefund(values);
  } else if (type.value === 'part') {
    handlePartRefund(values);
  } else if (type.value === 'initiative') {
    handleInitiativeRefund(values);
  }
};

// 全退
const handleAllRefund = async (values: any) => {
  const res = await orderFullRefund({
    orderId: orderData.value.id,
    ...values,
  });
  resetData();
};
//主动退
const handleInitiativeRefund = async (values: any) => {
  const res = await orderInitiativeRefund({
    orderId: orderData.value.orderId,
    orderItemId: orderData.value.orderItemId || orderData.value.id,
    orderDetailId: orderData.value.orderItemId ? orderData.value.id : null,
    ...values,
  });
  resetData();
};
//部分退
const handlePartRefund = async (values: any) => {
  const res = await orderPartRefund({
    orderId: orderData.value.id,
    orderItemId: orderData.value.orderItemId,
    orderDetailId: orderData.value.id,
    ...values,
  });
  resetData();
};

const resetData = () => {
  message.success('操作成功');
  emits('success');
  modalApi.close();
};

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    await handleSubmint();
  },
  onOpenChange(isOpen) {
    const data = modalApi.getData<any>();
    if (isOpen) {
      title.value = typeMap[data.type];
      type.value = data.type;
      orderData.value = { ...data.data };
      if (data.type !== 'part') {
        formApi.setState({
          schema: useRefundFormSchema().filter(
            (item) => item.fieldName !== 'refundPrice',
          ),
        });
      }
      console.log('data', orderData.value);
    }
  },
});

// 表单配置
const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    labelWidth: 100,
    componentProps: {
      class: 'w-full',
    },
  },
  fieldMappingTime: [],
  scrollToFirstError: true,
  schema: useRefundFormSchema(),
  showDefaultActions: false,
  // 一行显示1个
  wrapperClass: 'grid-cols-1',
});
</script>
<template>
  <Modal :title="title">
    <Form></Form>
  </Modal>
</template>
