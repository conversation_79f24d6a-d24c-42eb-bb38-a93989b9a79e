<template>
  <Page auto-content-height>
    <FormModel @success="onRefresh"></FormModel>
    <Grid>
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          新增客户
        </Button>
      </template>
      <template #customerStatus="{ row }">
        <Tag :color="customerStatusColor(row)" size="small">
          {{ useCustomerStatus(row) }}
        </Tag>
      </template>
      <template #tableTags="{ row }">
        <Tag v-for="tag in row.tags.split(',')" :key="tag" color="blue">
          {{ tag }}
        </Tag>
      </template>
    </Grid>
  </Page>
</template>
<script setup lang="ts">
import { Page } from '@vben/common-ui';
import {
  useGridFormSchema,
  useColumns,
  useCustomerStatus,
  customerStatusColor,
} from './data';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { getCustomerList, deleteCustomer } from '#/api/sale/customer';
import { Tag, message, Button } from 'ant-design-vue';
import { Plus } from '@vben/icons';
import { useVbenModal } from '@vben/common-ui';
import form from './modules/form.vue';
import { $t } from '#/locales';
const [FormModel, formModelApi] = useVbenModal({
  connectedComponent: form,
  destroyOnClose: true,
});

function onActionClick(e: any) {
  switch (e.code) {
    case 'delete': {
      // 处理删除
      onDelete(e.row);
      break;
    }
    case 'edit': {
      // 处理编辑
      onEdit(e.row);
      break;
    }
  }
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 2,
    wrapperClass: 'grid grid-cols-4 md:grid-cols-4 lg:grid-cols-5 gap-4',
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const res: any = await getCustomerList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

function onEdit(row: any) {
  formModelApi.setData(row).open();
}

function onDelete(row: any) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.name]),
    duration: 0,
    key: 'action_process_msg',
  });
  deleteCustomer({ id: row.id })
    .then(() => {
      message.success('删除成功');
      onRefresh();
      hideLoading();
    })
    .catch(() => {
      hideLoading();
    });
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formModelApi.setData({}).open();
}
</script>
