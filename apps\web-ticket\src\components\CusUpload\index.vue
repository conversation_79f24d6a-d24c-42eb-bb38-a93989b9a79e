<script lang="ts" setup>
import type { UploadProps } from 'ant-design-vue';

import { ref } from 'vue';

import { message, Modal, Upload } from 'ant-design-vue';

import { customOssUpload } from '#/utils/aliyun-oss';

// ===========================================================================
const props = defineProps({
  // 上传文件的最大数量，默认为 9
  maxCount: {
    type: Number,
    default: 9,
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true,
  },
  // 单个文件的最大体积限制，默认为 1M
  maxFileSize: {
    type: Number,
    default: 1,
  },
  // 上传文件的类型标签，默认为 fileTag
  fileTypeTag: {
    type: String,
    default: 'fileTag',
  },
});

// ===========================================================================
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

const fileList: any = ref<UploadProps['fileList']>([]);

// 预览 ========================================================================================
const handleCancel = () => {
  previewVisible.value = false;
  previewTitle.value = '';
};
const handlePreview = async (file: UploadProps['fileList'][number]) => {
  previewImage.value = file.url || file.preview;
  previewVisible.value = true;
  previewTitle.value =
    file.name || file.url.slice(Math.max(0, file.url.lastIndexOf('/') + 1));
};

// 上传校验 ========================================================================================
const prevent = (key: any) => {
  if (key) {
    setTimeout(() => {
      fileList.value = fileList.value.filter(
        (item: any) => item.lastModified !== key,
      );
    }, 300);
  }
};
const beforeUpload: UploadProps['beforeUpload'] = async (file: any) => {
  if (file.size > props.maxFileSize * 1024 * 1024) {
    message.error(`文件 ${file.name} 大小超过 ${props.maxFileSize}M`);
    prevent(file.lastModified);
    return false;
  }
  return true;
};

// 自定义上传 ===========================================================================================
const customRequest = async (file: any) => {
  console.log('🚀 ~ customRequest ~ file:', file);
  await customOssUpload({
    file,
    fileTypeTag: props.fileTypeTag,
    chunkSize: 1 * 1024 * 1024, // 1M
    onSuccess(res, file) {
      if (res) {
        fileList.value.push({
          uid: file.uid,
          name: file.name,
          status: 'done',
          url: res.url,
        });
      }
    },
  });
};
</script>
<template>
  <div class="clearfix">
    <Upload
      v-model:file-list="fileList"
      action=""
      :before-upload="beforeUpload"
      :custom-request="customRequest"
      list-type="picture-card"
      :max-count="props.maxCount"
      :multiple="props.maxCount > 1 && props.multiple"
      @preview="handlePreview"
    >
      <div v-if="fileList.length < 8">
        <div class="icon-[mdi--cloud-upload-outline] size-8"></div>
        <div>上传</div>
      </div>
    </Upload>
    <Modal
      :open="previewVisible"
      :title="previewTitle"
      :footer="null"
      @cancel="handleCancel"
    >
      <img alt="example" style="width: 100%" :src="previewImage" />
    </Modal>
  </div>
</template>
<style scoped lang="scss"></style>
