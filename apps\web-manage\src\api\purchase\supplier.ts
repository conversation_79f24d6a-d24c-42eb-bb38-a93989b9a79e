import { requestClient } from '#/api/request';

// 获取所有供应商
async function allSupplier(params: any) {
  return requestClient.get('/pur/distributor/all', { params: { ...params } });
}

// 获取所有供应商分页
async function listSupplier(params: any) {
  return requestClient.get('/pur/distributor/list', { params: { ...params } });
}

// 获取单个供应商信息
async function getInfo(params: any) {
  return requestClient.get('/pur/distributor/info', { params: { ...params } });
}

// 新增供应商
async function createSupplier(data: any) {
  return requestClient.post('/pur/distributor/create', data);
}
// 修改供应商
async function updateSupplier(data: any) {
  return requestClient.post('/pur/distributor/update', data);
}
// 删除供应商
async function deleteSupplier(data: any) {
  return requestClient.post('/pur/distributor/delete', data);
}

export {
  allSupplier,
  listSupplier,
  getInfo,
  createSupplier,
  updateSupplier,
  deleteSupplier,
};
