import { defineOverridesPreferences } from '@vben/preferences';

/**
 * @description 项目配置文件
 * 只需要覆盖项目中的一部分配置，不需要的配置不用覆盖，会自动使用默认配置
 * !!! 更改配置后请清空缓存，否则可能不生效
 */
export const overridesPreferences = defineOverridesPreferences({
  // overrides
  app: {
    // 菜单权限模式
    accessMode: 'both',
    // 是否开启检查更新
    enableCheckUpdates: true,
    // 检查更新的时间间隔，单位为分钟
    checkUpdatesInterval: 10,
    name: import.meta.env.VITE_APP_TITLE,
    shortName: import.meta.env.VITE_APP_SHORT_TITLE,
    layout: 'sidebar-mixed-nav', // sidebar-mixed-nav
    watermark: false,
    locale: 'zh-CN',
    dynamicTitle: true,
    enablePreferences: true,
  },
  theme: {
    mode: 'light',
    radius: '0.5',
  },
  widget: {
    languageToggle: false,
  },
  breadcrumb: {
    showHome: true,
  },
  copyright: {
    companyName: '智慧票务',
    companySiteLink: 'https://www.baidu.com',
    date: '2025',
    enable: true,
    icp: '',
    icpLink: '',
  },
  footer: {
    enable: false,
    fixed: false,
  },
  logo: {
    enable: true,
    source: '/static/logo.svg',
  },
  sidebar: {
    width: 220,
    fixedButton: false,
  },
  tabbar: {
    maxCount: 20,
    middleClickToClose: true,
  },
  transition: {
    enable: false,
    loading: false,
  },
  shortcutKeys: {
    globalPreferences: false,
  },
});
