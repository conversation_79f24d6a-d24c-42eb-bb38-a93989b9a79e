import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs, markRaw } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

import { getAllScenicList } from '#/api';
import CouponType from './modules/formItem/couponType.vue';
import GetTime from './modules/formItem/getTime.vue';
import ValidType from './modules/formItem/validType.vue';
import ThresholdType from './modules/formItem/thresholdType.vue';
export function useGridFormSchema(): VbenFormSchema[] {
  return [];
}

export function useAddFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'couponName',
      label: '优惠券名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入优惠券名称',
        allowClear: true,
      },
    },
    // {
    //   component: 'RadioGroup',
    //   fieldName: 'couponType',
    //   label: '优惠券类型',
    //   rules: 'required',
    //   componentProps: {
    //     options: [
    //       { label: '满减劵', value: 1 },
    //       { label: '折扣券', value: 2 },
    //     ],
    //   },
    //   defaultValue: 1,
    // },
    {
      component: markRaw(CouponType),
      fieldName: 'couponTypes',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '优惠券类型',
      rules: 'required',
      defaultValue: [1, null],
      disabledOnChangeListener: false,
    },
    {
      component: markRaw(GetTime),
      fieldName: 'getTimeTypes',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '领取日期',
      rules: 'required',
      defaultValue: [1, []],
      disabledOnChangeListener: false,
    },
    {
      component: markRaw(ValidType),
      fieldName: 'validTypes',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '有效期',
      rules: 'required',
      defaultValue: [1, null],
      disabledOnChangeListener: false,
    },
    {
      component: markRaw(ThresholdType),
      fieldName: 'thresholdTypes',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '使用门槛',
      rules: 'required',
      defaultValue: [1, null],
      disabledOnChangeListener: false,
    },
    {
      component: 'InputNumber',
      fieldName: 'stockNum',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '库存数量',
      rules: 'required',
      componentProps: {
        placeholder: '请输入库存数量',
        allowClear: true,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'dayLimit',
      label: '单人单日限领',
      rules: 'required',
      help: '0为不限制',
      componentProps: {
        placeholder: '请输入单人单日限领数',
        allowClear: true,
      },
      defaultValue: 0,
    },
    {
      component: 'InputNumber',
      fieldName: 'userLimit',
      label: '单人限领',
      rules: 'required',
      help: '0为不限制',
      componentProps: {
        placeholder: '请输入单人限领数',
        allowClear: true,
      },
      defaultValue: 0,
    },
    {
      component: 'TextEditor',
      fieldName: 'couponDesc',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '使用说明',
      componentProps: {
        class: 'w-full',
        height: '300px',
        fileTypeTag: 'ticket',
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        allowClear: true,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'listorder',
      label: '排序',
      componentProps: {
        placeholder: '请输入排序',
        allowClear: true,
      },
      defaultValue: 0,
    },
    {
      component: 'RadioGroup',
      fieldName: 'scenicScopeType',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '适用景区',
      rules: 'required',
      componentProps: {
        options: [
          { label: '全部', value: 1 },
          { label: '部分', value: 2 },
        ],
      },
      defaultValue: 1,
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicScope',
      label: '适用景区范围',
      rules: 'selectRequired',
      componentProps: {
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        api: getAllScenicList,
        placeholder: '请选择适用景区范围',
        allowClear: true,
        mode: 'multiple',
      },
      dependencies: {
        show: ({ scenicScopeType }) => scenicScopeType === 2,
        triggerFields: ['scenicScopeType'],
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'ticketScopeType',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '适用门票',
      rules: 'required',
      componentProps: {
        options: [
          { label: '全部', value: 1 },
          { label: '部分', value: 2 },
        ],
      },
      defaultValue: 1,
    },
    {
      component: 'ApiSelect',
      fieldName: 'ticketScope',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '适用门票范围',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择适用门票范围',
        allowClear: true,
        mode: 'multiple',
      },
      dependencies: {
        show: ({ ticketScopeType }) => ticketScopeType === 2,
        componentProps(values) {
          if (values.scenicScope && values.scenicScope.length) {
            return (values.ticketScope = []);
          }
          return {};
        },
        triggerFields: ['scenicScope', 'ticketScopeType'],
      },
    },
  ];
}
export function useEditFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'couponName',
      label: '优惠券名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入优惠券名称',
        allowClear: true,
      },
    },
    {
      component: markRaw(ThresholdType),
      fieldName: 'thresholdTypes',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '使用门槛',
      rules: 'required',
      defaultValue: [1, null],
      disabledOnChangeListener: false,
    },
    // {
    //   component: 'RadioGroup',
    //   fieldName: 'thresholdType',
    //   label: '使用门槛',
    //   rules: 'required',
    //   componentProps: {
    //     options: [
    //       { label: '无门槛', value: 1 },
    //       { label: '最低消费', value: 2 },
    //     ],
    //   },
    //   defaultValue: 1,
    // },
    // {
    //   component: 'InputNumber',
    //   fieldName: 'thresholdPrice',
    //   label: '最低消费金额',
    //   rules: 'required',
    //   componentProps: {
    //     placeholder: '请输入最低消费金额',
    //     allowClear: true,
    //   },
    //   dependencies: {
    //     show: ({ thresholdType }) => thresholdType === 2,
    //     triggerFields: ['thresholdType'],
    //   },
    //   suffix: '元',
    // },
    {
      component: 'InputNumber',
      fieldName: 'stockNum',
      label: '库存数量',
      rules: 'required',
      componentProps: {
        placeholder: '请输入库存数量',
        allowClear: true,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'dayLimit',
      label: '单人单日限领',
      rules: 'required',
      help: '0为不限制',
      componentProps: {
        placeholder: '请输入单人单日限领数',
        allowClear: true,
      },
      defaultValue: 0,
    },
    {
      component: 'InputNumber',
      fieldName: 'userLimit',
      label: '单人限领',
      rules: 'required',
      help: '0为不限制',
      componentProps: {
        placeholder: '请输入单人限领数',
        allowClear: true,
      },
      defaultValue: 0,
    },
    {
      component: 'TextEditor',
      fieldName: 'couponDesc',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '使用说明',
      componentProps: {
        class: 'w-full',
        height: '300px',
        fileTypeTag: 'ticket',
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        allowClear: true,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'listorder',
      label: '排序',
      componentProps: {
        placeholder: '请输入排序',
        allowClear: true,
      },
      defaultValue: 0,
    },
  ];
}

// 表格列
export function useSendFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userIdList',
      label: '选择用户',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择用户',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'couponIdList',
      label: '选择优惠券',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择优惠券',
        allowClear: true,
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        allowClear: true,
      },
    },
  ];
}
