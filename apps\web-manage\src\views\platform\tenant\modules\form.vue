<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { createTenant, updateTenant } from '#/api';
import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<any>();

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  // 一行显示1个
  wrapperClass: 'grid-cols-1',
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
});

const id = ref();
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<any>();
      formApi.resetForm();
      if (data) {
        const newFormData = { ...data };
        if (data.tenantLogo) {
          newFormData.tenantLogo = data.tenantLogo
            .split(',')
            .map((item: any) => {
              return {
                uid: item,
                name: item,
                status: 'done',
                url: item,
                response: {
                  url: item,
                },
              };
            });
        } else {
          newFormData.tenantLogo = [];
        }
        formData.value = newFormData;
        id.value = data.id;
        formApi.setValues(formData.value);
      } else {
        id.value = undefined;
      }
    }
  },
});

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();
  // 处理图片字段
  ['tenantLogo'].forEach((field) => {
    if (values[field]) {
      values[field] = values[field].map((item: any) => item.url).join(',');
    }
  });

  return values;
};

// 提交表单
const handleSubmit = async () => {
  const values = await processFormValues();
  if (!values) return;
  modalApi.lock();
  try {
    if (formData.value.id) {
      await updateTenant({ id: id.value, ...values });
    } else {
      await createTenant(values);
    }
    message.success('保存成功');
    emits('success');
    modalApi.close();
  } catch (error) {
    modalApi.unlock();
  }
};

const getModalTitle = computed(() => {
  return formData.value?.id ? '编辑企业' : '新增企业';
});
</script>
<template>
  <Modal class="w-[600px]" :title="getModalTitle">
    <Form></Form>
  </Modal>
</template>
