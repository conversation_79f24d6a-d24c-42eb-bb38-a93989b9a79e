<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import {
  Checkbox,
  Input,
  Row,
  Col,
  InputNumber,
  Divider,
  QRCode,
} from 'ant-design-vue';

const props = defineProps({
  value: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['update:value']);
// 配置项状态
let config = reactive({
  // 页面设置
  page: {
    width: 58, // 小票纸宽度(mm) - 常见58mm热敏纸
    marginTop: 2,
    marginBottom: 2,
    marginLeft: 2,
    marginRight: 2,
  },
  // 抬头设置
  header: {
    areaName: true,
    ticketType: true,
  },
  // 顶部设置
  top: {
    orderNumber: true,
    saleWindow: true,
    saleTime: true,
    operator: true,
  },
  // 中部设置
  middle: {
    memberName: true,
    memberCard: true,
    phone: true,
  },
  // 底部设置
  bottom: {
    receivedAmount: true,
    actualAmount: true,
    paymentMethod: true,
    change: true,
  },
  // 尾注设置
  footer: {
    note1: true,
    note1Text: '',
    note2: true,
    note2Text: '',
  },
});

// 计算预览区样式 - 按实际小票比例
const previewStyle = computed(() => ({
  width: `${config.page.width * 4}px`, // 58mm * 4 = 232px
  padding: `${config.page.marginTop * 4}px ${config.page.marginRight * 4}px ${config.page.marginBottom * 4}px ${config.page.marginLeft * 4}px`,
  fontFamily: 'monospace', // 使用等宽字体模拟小票打印效果
}));

watch(
  () => config,
  (newConfig) => {
    emit('update:value', {
      margins: [
        newConfig.page.marginLeft * 4,
        newConfig.page.marginTop * 4,
        newConfig.page.marginRight * 4,
        newConfig.page.marginBottom * 4,
      ],
      width: newConfig.page.width * 4,
      templateConfig: newConfig,
    });
  },
  { deep: true, immediate: true },
);
watch(
  () => props.value,
  (newValue) => {
    if (newValue && newValue.templateConfig) {
      Object.assign(config, newValue.templateConfig);
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
</script>

<template>
  <div class="flex gap-6">
    <!-- 左侧预览区 -->
    <div class="w-1/3">
      <h3 class="mb-4 text-lg font-semibold">模板设计</h3>
      <div
        class="flex min-h-[600px] items-start justify-center border border-gray-300 bg-gray-50 p-4"
      >
        <!-- 小票预览 -->
        <div class="border bg-white shadow-lg" :style="previewStyle">
          <!-- 抬头 -->
          <div
            v-if="config.header.areaName || config.header.ticketType"
            class="mb-1 text-center"
          >
            <div
              v-if="config.header.areaName"
              class="text-[14px] font-bold leading-[1.8]"
            >
              演示景区
            </div>
            <div v-if="config.header.ticketType" class="text-[12px] leading-[1.8]">
              充值小票【开卡充值】
            </div>
          </div>

          <!-- 分割线 -->
          <Divider
            v-if="config.header.areaName || config.header.ticketType"
            class="!my-2 !text-[14px]"
            dashed
          />

          <!-- 顶部信息 -->
          <div class="text-[14px] leading-normal">
            <div v-if="config.top.orderNumber" class="mb-1.5 flex">
              <span
                class="block w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >订单号</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">12345678990</span>
            </div>
            <div v-if="config.top.saleWindow" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >售票窗口</span
              >
              <span class="opacity-50">：</span>

              <span class="flex-1">大门1号窗口</span>
            </div>
            <div v-if="config.top.saleTime" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >售票时间</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">2025-07-23 11:38:38</span>
            </div>
            <div v-if="config.top.operator" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >操作员</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">售票员A</span>
            </div>
          </div>

          <!-- 分割线 -->
          <Divider
            v-if="
              config.top.orderNumber ||
              config.top.saleWindow ||
              config.top.saleTime ||
              config.top.operator
            "
            class="!my-2 !text-[14px]"
            dashed
          />

          <!-- 中部信息 -->
          <div class="text-[14px] leading-normal">
            <div v-if="config.middle.memberName" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >姓名</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">XXX</span>
            </div>
            <div v-if="config.middle.memberCard" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >卡号</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">12345674890</span>
            </div>
            <div v-if="config.middle.phone" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >手机号</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">138****8888</span>
            </div>
            <div class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >充值金额</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">1200.00</span>
            </div>
            <div class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >赠送金额</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">0.00</span>
            </div>
          </div>

          <!-- 分割线 -->
          <Divider class="!my-2 !text-[14px]" dashed />

          <!-- 底部信息 -->
          <div class="text-[14px] leading-normal">
            <div v-if="config.bottom.receivedAmount" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >应收金额</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">1200.00</span>
            </div>
            <div v-if="config.bottom.actualAmount" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >实收金额</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">1200.00</span>
            </div>
            <div v-if="config.bottom.paymentMethod" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >结算方式</span
              >
              <span class="opacity-50">：</span>
              <span class="flex-1">扫码</span>
            </div>
            <div v-if="config.bottom.change" class="mb-1.5 flex">
              <span
                class="w-[56px] text-justify opacity-50"
                style="text-align-last: justify"
                >找零</span
              ><span class="opacity-50">：</span>
              <span class="flex-1">0.00</span>
            </div>
          </div>

          <!-- 分割线 -->
          <Divider
            v-if="config.footer.note1 || config.footer.note2"
            class="!my-2 !text-[14px]"
            dashed
          />

          <!-- 尾注 -->
          <div
            v-if="config.footer.note1 || config.footer.note2"
            class="text-center text-[14px] leading-tight"
          >
            <div v-if="config.footer.note1" class="mb-1">
              {{ config.footer.note1Text || 'XXXXXX景区' }}
            </div>
            <div v-if="config.footer.note2">
              {{ config.footer.note2Text || '欢迎下次光临' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧配置区 -->
    <div class="w-2/3">
      <div class="space-y-6">
        <!-- 页面设置 -->
        <div>
          <h4 class="mb-3 font-semibold">页面设置</h4>
          <Row :gutter="16">
            <Col :span="24">
              <div class="mb-2 flex items-center gap-2">
                <span class="w-16 text-[14px]">纸张宽度:</span>
                <InputNumber
                  v-model:value="config.page.width"
                  :min="40"
                  :max="80"
                  size="small"
                />
                <span class="text-xs text-gray-500">mm (常用: 58mm/80mm)</span>
              </div>
            </Col>
          </Row>
          <Row :gutter="8">
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-12 text-[14px]">上边距:</span>
                <InputNumber
                  v-model:value="config.page.marginTop"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-xs text-gray-500">mm</span>
              </div>
            </Col>
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-12 text-[14px]">下边距:</span>
                <InputNumber
                  v-model:value="config.page.marginBottom"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-xs text-gray-500">mm</span>
              </div>
            </Col>
          </Row>
          <Row :gutter="8" class="mt-2">
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-12 text-[14px]">左边距:</span>
                <InputNumber
                  v-model:value="config.page.marginLeft"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-xs text-gray-500">mm</span>
              </div>
            </Col>
            <Col :span="12">
              <div class="flex items-center gap-2">
                <span class="w-12 text-[14px]">右边距:</span>
                <InputNumber
                  v-model:value="config.page.marginRight"
                  :min="0"
                  :max="10"
                  size="small"
                />
                <span class="text-xs text-gray-500">mm</span>
              </div>
            </Col>
          </Row>
        </div>

        <!-- 抬头设置 -->
        <div>
          <h4 class="mb-3 font-semibold">抬头设置</h4>
          <Row :gutter="16">
            <Col :span="12">
              <Checkbox v-model:checked="config.header.areaName"
                >景区名称</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.header.ticketType"
                >小票类型</Checkbox
              >
            </Col>
          </Row>
        </div>

        <!-- 顶部设置 -->
        <div>
          <h4 class="mb-3 font-semibold">顶部设置</h4>
          <Row :gutter="16">
            <Col :span="12">
              <Checkbox v-model:checked="config.top.orderNumber"
                >订单号</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.top.saleWindow"
                >售票窗口</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.top.saleTime"
                >售票时间</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.top.operator">操作员</Checkbox>
            </Col>
          </Row>
        </div>

        <!-- 中部设置 -->
        <div>
          <h4 class="mb-3 font-semibold">中部设置</h4>
          <Row :gutter="16">
            <Col :span="12">
              <Checkbox v-model:checked="config.middle.memberName"
                >姓名</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.middle.memberCard"
                >卡号</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.middle.phone">手机号</Checkbox>
            </Col>
          </Row>
        </div>

        <!-- 底部设置 -->
        <div>
          <h4 class="mb-3 font-semibold">底部设置</h4>
          <Row :gutter="16">
            <Col :span="12">
              <Checkbox v-model:checked="config.bottom.receivedAmount"
                >应收金额</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.bottom.actualAmount"
                >实收金额</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.bottom.paymentMethod"
                >结算方式</Checkbox
              >
            </Col>
            <Col :span="12">
              <Checkbox v-model:checked="config.bottom.change">找零</Checkbox>
            </Col>
          </Row>
        </div>

        <!-- 尾注设置 -->
        <div>
          <h4 class="mb-3 font-semibold">尾注设置</h4>
          <div class="space-y-3">
            <div class="flex items-center gap-2">
              <Checkbox v-model:checked="config.footer.note1" class="w-[80px]"
                >尾注1</Checkbox
              >
              <Input
                v-model:value="config.footer.note1Text"
                placeholder="请输入尾注1内容"
                size="small"
              />
            </div>
            <div class="flex items-center gap-2">
              <Checkbox v-model:checked="config.footer.note2" class="w-[80px]"
                >尾注2</Checkbox
              >
              <Input
                v-model:value="config.footer.note2Text"
                placeholder="请输入尾注2内容"
                size="small"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
