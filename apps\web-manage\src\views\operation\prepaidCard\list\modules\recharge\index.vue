div
<script setup lang="ts">
import { getConfigInfoByScene } from '#/api';
import { ref, onMounted } from 'vue';
import { message, InputNumber } from 'ant-design-vue';

const emit = defineEmits(['update:value']);

const rechargeList = ref<any[]>([]);
const getRechargeConfig = async () => {
  const res = await getConfigInfoByScene({ scene: 'prepaidCard' });
  console.log(res, 'res');
  rechargeList.value = res.levelConfig;
};
onMounted(() => {
  getRechargeConfig();
});

const selectData = ref<any>({});
const isCustom = ref<any>(false);
const selectRecgearge = (item: any) => {
  isCustom.value = false;
  selectData.value = item;
  emit('update:value', selectData.value);
};

const inputValue = ref<any>(undefined);
const amountChange = (val: any) => {
  if (val < 0) {
    message.warning('金额不能小于0');
    return;
  }
  selectData.value = { amount: val, giftAmount: 0 };
  emit('update:value', selectData.value);
};
</script>
<template>
  <div class="flex w-full flex-wrap gap-1">
    <div
      class="flex h-[70px] w-[110px] cursor-pointer flex-col items-center justify-center rounded-md border border-gray-200"
      v-for="(item, index) in rechargeList"
      :key="index"
      @click="selectRecgearge(item)"
      :style="
        selectData.amount === item.amount && !isCustom
          ? 'border-color:hsl(var(--primary))'
          : ''
      "
    >
      <div class="text-md font-bold">￥{{ item.amount }}</div>
      <div class="text-sm text-gray-500">赠送{{ item.giftAmount }}</div>
    </div>
    <div
      class="flex h-[70px] w-[110px] cursor-pointer flex-col items-center justify-center rounded-md border border-gray-200"
      @click="isCustom = true"
      :style="isCustom ? 'border-color:hsl(var(--primary))' : ''"
    >
      <div v-if="!isCustom">自定义</div>
      <InputNumber
        v-else
        type="number"
        placeholder="金额"
        v-model:value="inputValue"
        :bordered="false"
        :step="0.01"
        :min="0.01"
        @change="amountChange"
      />
    </div>
  </div>
</template>
