<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { computed, ref, h, toRefs } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { useFormSchema } from '../data';
import { createTicket, updateTicket, getPrintTemplateAllList } from '#/api';
import { IconifyIcon } from '@vben/icons';
import CusDate from '#/components/CusDate/index.vue';
import CusUpload from '#/components/CusUpload/index.vue';
import ChildTicket from './childTicket/index.vue';
import { Button, message, Select } from 'ant-design-vue';

import { useAccessStore } from '@vben/stores';
const { accessAllEnums } = toRefs(useAccessStore());

const emits = defineEmits(['success']);
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
});

const formData = ref<Recordable<any>>({});

// 表单配置
const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    labelWidth: 120,
    disabled: computed(() => props.disabled),
    componentProps: {
      class: 'w-full',
    },
  },
  fieldMappingTime: [
    ['refundFee', ['refundFeeValue', 'refundFeeValueType'], null],
    ['reserve', ['isReserve', 'beforeDay'], null],
    ['purchaseD', ['isPurchaseDate', 'purchaseDate'], null],
    ['purchaseT', ['isPurchaseTime', 'purchaseTime'], null],
    ['writeOffT', ['isWriteOffTime', 'writeOffTime'], null],
  ],
  scrollToFirstError: true,
  schema: useFormSchema(),
  showDefaultActions: false,
  // 一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2',
});

// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    const data = modelApi.getData<any>();
    formApi.resetForm();
    if (isOpen) {
      getPrintTemplateList();
      if (data) {
        // 创建新对象而不是直接赋值
        const newFormData = { ...data };
        if (data.ticketCover) {
          newFormData.ticketCover = data.ticketCover
            .split(',')
            .map((item: any) => {
              return {
                uid: item,
                name: item,
                status: 'done',
                url: item,
                response: {
                  url: item,
                },
              };
            });
        } else {
          newFormData.ticketCover = [];
        }
        if (data.ticketImg) {
          newFormData.ticketImg = data.ticketImg.split(',').map((item: any) => {
            return {
              uid: item,
              name: item,
              status: 'done',
              url: item,
              response: {
                url: item,
              },
            };
          });
        } else {
          newFormData.ticketImg = [];
        }
        newFormData.entryTime = [data.entryBeginTime, data.entryEndTime];
        newFormData.reserve = [data.isReserve || 0, data.beforeDay];
        newFormData.purchaseDate = [
          data.purchaseBeginDate,
          data.purchaseEndDate,
        ];
        newFormData.purchaseD = [
          data.isPurchaseDate || 0,
          newFormData.purchaseDate,
        ];
        newFormData.purchaseTime = [
          data.purchaseBeginTime,
          data.purchaseEndTime,
        ];
        newFormData.purchaseT = [
          data.isPurchaseTime || 0,
          newFormData.purchaseTime,
        ];
        newFormData.writeOffTime = [
          data.writeOffBeginTime,
          data.writeOffEndTime,
        ];
        newFormData.writeOffT = [
          data.isWriteOffTime || 0,
          newFormData.writeOffTime,
        ];
        newFormData.validDate = [data.validBeginDate, data.validEndDate];
        newFormData.refundFee = [data.refundFeeValue, data.refundFeeValueType];
        // 赋值给formData.value
        formData.value = newFormData;
        id.value = data.id;
        formApi.setValues(formData.value);
      }
    }
  },
});

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();
  values.ticketCover = values.ticketCover
    .map((item: any) => item.url)
    .join(',');
  values.ticketImg = values.ticketImg.map((item: any) => item.url).join(',');
  values.entryBeginTime = values.entryTime[0];
  values.entryEndTime = values.entryTime[1];
  values.purchaseBeginDate = values.purchaseDate[0];
  values.purchaseEndDate = values.purchaseDate[1];
  values.purchaseBeginTime = values.purchaseTime[0];
  values.purchaseEndTime = values.purchaseTime[1];
  values.writeOffBeginTime = values.writeOffTime[0];
  values.writeOffEndTime = values.writeOffTime[1];
  values.validBeginDate = values.validDate[0];
  values.validEndDate = values.validDate[1];

  return values;
};

// 统一的提交处理函数
const handleSubmit = async () => {
  const values = await processFormValues();
  console.log(values, 'submit');
  if (!values) return;
  modelApi.lock();
  try {
    if (id.value) {
      await updateTicket({ id: id.value, model: 2, ...values });
    } else {
      await createTicket({ model: 2, ...values });
    }
    emits('success');
    modelApi.close();
  } catch (error) {
    modelApi.unlock();
  }
};

const getModelTitle = computed(() => {
  return formData.value?.id ? '编辑套票' : '新增套票';
});

const childTicketRef = ref<any>(null);
const scenicId = ref<any>(null);
const addTicketBtn = async () => {
  const values = await formApi.getValues();
  scenicId.value = values.scenicId;
  if (!scenicId.value) {
    message.warning('请先选择门票所属景区！');
    return;
  }
  childTicketRef.value[0].addTicket(scenicId.value);
};

// 获取打印模版列表
const printTemplateList = ref<any>([]);
const getPrintTemplateList = async () => {
  const res = await getPrintTemplateAllList({});
  printTemplateList.value = res.map((item: any) => {
    return {
      label: item.templateName,
      value: item.id,
      type: filterType(item.templateType),
    };
  });
};

const filterType = (val: any) => {
  return accessAllEnums.value.printTemplateType.list.find(
    (item: any) => item.value === val,
  )?.label;
};
</script>
<template>
  <Model class="w-[1200px]" :title="getModelTitle">
    <Form>
      <template #ticketImg="slotProps">
        <div>
          <CusUpload v-bind="slotProps"></CusUpload>
          <p class="flex items-center text-[12px] text-gray-500">
            <IconifyIcon icon="mdi:info" class="mr-1 size-4" />
            建议图片尺寸不小于375X211px，图片比例16:9，不超过9张
          </p>
        </div>
      </template>
      <template #unavailableDateList="slotProps">
        <CusDate v-bind="slotProps" class="w-full" />
      </template>
      <template #childList="slotProps">
        <div class="w-full">
          <Button
            type="primary"
            class="mb-2"
            :disabled="disabled"
            @click="addTicketBtn"
            >添加门票</Button
          >
          <ChildTicket v-bind="slotProps" ref="childTicketRef"></ChildTicket>
        </div>
      </template>
      <template #windowPrintTemplate="slotProps">
        <Select v-bind="slotProps" :options="printTemplateList">
          <template #option="{ value: val, label, type }">
            <div class="flex items-center justify-between" :aria-label="val">
              <span>{{ label }}</span>
              <span class="text-xs text-gray-500">{{ type }}</span>
            </div>
          </template>
        </Select>
      </template>
      <template #handPrintTemplate="slotProps">
        <Select v-bind="slotProps" :options="printTemplateList">
          <template #option="{ value: val, label, type }">
            <div class="flex items-center justify-between" :aria-label="val">
              <span>{{ label }}</span>
              <span class="text-xs text-gray-500">{{ type }}</span>
            </div>
          </template>
        </Select>
      </template>
      <template #selfDevicePrintTemplate="slotProps">
        <Select v-bind="slotProps" :options="printTemplateList">
          <template #option="{ value: val, label, type }">
            <div class="flex items-center justify-between" :aria-label="val">
              <span>{{ label }}</span>
              <span class="text-xs text-gray-500">{{ type }}</span>
            </div>
          </template>
        </Select>
      </template>
    </Form>
  </Model>
</template>
