<script setup lang="ts">
import { useVbenDrawer } from '@vben/common-ui';
import { ref, reactive, computed } from 'vue';
import {
  Checkbox,
  Input,
  Row,
  Col,
  InputNumber,
  Divider,
} from 'ant-design-vue';

const id = ref();

// 配置项状态
const config = reactive({
  // 页面设置
  page: {
    width: 58, // 小票纸宽度(mm) - 常见58mm热敏纸
    marginTop: 2,
    marginBottom: 2,
    marginLeft: 2,
    marginRight: 2,
  },
  // 抬头设置
  header: {
    areaName: true,
    ticketType: true,
  },
  // 顶部设置
  top: {
    orderNumber: true,
    saleWindow: true,
    saleTime: true,
    operator: true,
  },
  // 中部设置
  middle: {
    memberName: true,
    memberCard: true,
    memberTicket: true,
  },
  // 底部设置
  bottom: {
    receivedAmount: true,
    actualAmount: true,
    paymentMethod: true,
    change: true,
  },
  // 尾注设置
  footer: {
    note1: true,
    note1Text: 'XXXXX旅游景点',
    note2: true,
    note2Text: '欢迎下次光临',
  },
});

// 计算预览区样式 - 按实际小票比例
const previewStyle = computed(() => ({
  width: `${config.page.width * 4}px`, // 58mm * 4 = 232px
  padding: `${config.page.marginTop * 4}px ${config.page.marginRight * 4}px ${config.page.marginBottom * 4}px ${config.page.marginLeft * 4}px`,
  fontFamily: 'monospace', // 使用等宽字体模拟小票打印效果
}));

const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {},
  onOpenChange(isOpen) {
    const data = drawerApi.getData<any>();
    if (isOpen) {
      id.value = data.id;
    }
  },
});
</script>

<template>
  <Drawer class="w-[1200px]" title="打印配置">
    <div class="flex gap-6">
      <!-- 左侧预览区 -->
      <div class="w-1/3">
        <h3 class="mb-4 text-lg font-semibold">模板设计</h3>
        <div
          class="flex min-h-[600px] items-start justify-center border border-gray-300 bg-gray-50 p-4"
        >
          <!-- 小票预览 -->
          <div class="border bg-white shadow-lg" :style="previewStyle">
            <!-- 抬头 -->
            <div
              v-if="config.header.areaName || config.header.ticketType"
              class="mb-1 text-center"
            >
              <div v-if="config.header.areaName" class="text-sm font-bold">
                演示景区
              </div>
              <div v-if="config.header.ticketType" class="text-xs">
                会员小票【存根】
              </div>
            </div>

            <!-- 分割线 -->
            <Divider
              v-if="config.header.areaName || config.header.ticketType"
              class="!my-1 !text-xs"
            />

            <!-- 顶部信息 -->
            <div class="text-xs leading-tight">
              <div v-if="config.top.orderNumber" class="mb-1">
                订单号: 202310231137001
              </div>
              <div v-if="config.top.saleWindow" class="mb-1">
                售票窗口: 东大门1号
              </div>
              <div v-if="config.top.saleTime" class="mb-1">
                售票时间: 2023-10-23 11:37:38
              </div>
              <div v-if="config.top.operator" class="mb-1">操作员: 售票员A</div>
            </div>

            <!-- 分割线 -->
            <Divider
              v-if="
                config.top.orderNumber ||
                config.top.saleWindow ||
                config.top.saleTime ||
                config.top.operator
              "
              class="!my-1 !text-xs"
            />

            <!-- 中部信息 -->
            <div class="text-xs leading-tight">
              <div v-if="config.middle.memberName" class="mb-1">
                会员姓名: 张文琦
              </div>
              <div v-if="config.middle.memberCard" class="mb-1">
                会员卡号: 14612798
              </div>
              <div v-if="config.middle.memberTicket" class="mb-1">
                会员票类: 会员票类名称
              </div>
              <div class="mb-1">充值金额: 1200.00</div>
              <div class="mb-1">充值次数: 999</div>
            </div>

            <!-- 分割线 -->
            <Divider class="!my-1 !text-xs" />

            <!-- 底部信息 -->
            <div class="text-xs leading-tight">
              <div v-if="config.bottom.receivedAmount" class="mb-1">
                应收金额: 400.12
              </div>
              <div v-if="config.bottom.actualAmount" class="mb-1">
                实收金额: 410.00
              </div>
              <div v-if="config.bottom.paymentMethod" class="mb-1">
                结算方式: 扫码
              </div>
              <div v-if="config.bottom.change" class="mb-1">找零: 9.88</div>
            </div>

            <!-- 分割线 -->
            <Divider
              v-if="config.footer.note1 || config.footer.note2"
              class="!my-1 !text-xs"
            />

            <!-- 尾注 -->
            <div
              v-if="config.footer.note1 || config.footer.note2"
              class="text-center text-xs leading-tight"
            >
              <div v-if="config.footer.note1" class="mb-1">
                {{ config.footer.note1Text }}
              </div>
              <div v-if="config.footer.note2">
                {{ config.footer.note2Text }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧配置区 -->
      <div class="w-2/3">
        <div class="space-y-6">
          <!-- 页面设置 -->
          <div>
            <h4 class="mb-3 font-semibold">页面设置</h4>
            <Row :gutter="16">
              <Col :span="24">
                <div class="mb-2 flex items-center gap-2">
                  <span class="w-16 text-sm">纸张宽度:</span>
                  <InputNumber
                    v-model:value="config.page.width"
                    :min="40"
                    :max="80"
                    size="small"
                  />
                  <span class="text-xs text-gray-500"
                    >mm (常用: 58mm/80mm)</span
                  >
                </div>
              </Col>
            </Row>
            <Row :gutter="8">
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-12 text-sm">上边距:</span>
                  <InputNumber
                    v-model:value="config.page.marginTop"
                    :min="0"
                    :max="10"
                    size="small"
                  />
                  <span class="text-xs text-gray-500">mm</span>
                </div>
              </Col>
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-12 text-sm">下边距:</span>
                  <InputNumber
                    v-model:value="config.page.marginBottom"
                    :min="0"
                    :max="10"
                    size="small"
                  />
                  <span class="text-xs text-gray-500">mm</span>
                </div>
              </Col>
            </Row>
            <Row :gutter="8" class="mt-2">
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-12 text-sm">左边距:</span>
                  <InputNumber
                    v-model:value="config.page.marginLeft"
                    :min="0"
                    :max="10"
                    size="small"
                  />
                  <span class="text-xs text-gray-500">mm</span>
                </div>
              </Col>
              <Col :span="12">
                <div class="flex items-center gap-2">
                  <span class="w-12 text-sm">右边距:</span>
                  <InputNumber
                    v-model:value="config.page.marginRight"
                    :min="0"
                    :max="10"
                    size="small"
                  />
                  <span class="text-xs text-gray-500">mm</span>
                </div>
              </Col>
            </Row>
          </div>

          <!-- 抬头设置 -->
          <div>
            <h4 class="mb-3 font-semibold">抬头设置</h4>
            <Row :gutter="16">
              <Col :span="12">
                <Checkbox v-model:checked="config.header.areaName"
                  >景区名称</Checkbox
                >
              </Col>
              <Col :span="12">
                <Checkbox v-model:checked="config.header.ticketType"
                  >小票类型</Checkbox
                >
              </Col>
            </Row>
          </div>

          <!-- 顶部设置 -->
          <div>
            <h4 class="mb-3 font-semibold">顶部设置</h4>
            <Row :gutter="16">
              <Col :span="12">
                <Checkbox v-model:checked="config.top.orderNumber"
                  >订单号</Checkbox
                >
              </Col>
              <Col :span="12">
                <Checkbox v-model:checked="config.top.saleWindow"
                  >售票窗口</Checkbox
                >
              </Col>
              <Col :span="12">
                <Checkbox v-model:checked="config.top.saleTime"
                  >售票时间</Checkbox
                >
              </Col>
              <Col :span="12">
                <Checkbox v-model:checked="config.top.operator"
                  >操作员</Checkbox
                >
              </Col>
            </Row>
          </div>

          <!-- 中部设置 -->
          <div>
            <h4 class="mb-3 font-semibold">中部设置</h4>
            <Row :gutter="16">
              <Col :span="12">
                <Checkbox v-model:checked="config.middle.memberName"
                  >会员姓名</Checkbox
                >
              </Col>
              <Col :span="12">
                <Checkbox v-model:checked="config.middle.memberCard"
                  >会员卡号</Checkbox
                >
              </Col>
              <Col :span="12">
                <Checkbox v-model:checked="config.middle.memberTicket"
                  >会员票类</Checkbox
                >
              </Col>
            </Row>
          </div>

          <!-- 底部设置 -->
          <div>
            <h4 class="mb-3 font-semibold">底部设置</h4>
            <Row :gutter="16">
              <Col :span="12">
                <Checkbox v-model:checked="config.bottom.receivedAmount"
                  >应收金额</Checkbox
                >
              </Col>
              <Col :span="12">
                <Checkbox v-model:checked="config.bottom.actualAmount"
                  >实收金额</Checkbox
                >
              </Col>
              <Col :span="12">
                <Checkbox v-model:checked="config.bottom.paymentMethod"
                  >结算方式</Checkbox
                >
              </Col>
              <Col :span="12">
                <Checkbox v-model:checked="config.bottom.change">找零</Checkbox>
              </Col>
            </Row>
          </div>

          <!-- 尾注设置 -->
          <div>
            <h4 class="mb-3 font-semibold">尾注设置</h4>
            <div class="space-y-3">
              <div class="flex items-center gap-2">
                <Checkbox v-model:checked="config.footer.note1">尾注1</Checkbox>
                <Input
                  v-model:value="config.footer.note1Text"
                  placeholder="请输入尾注1内容"
                  size="small"
                />
              </div>
              <div class="flex items-center gap-2">
                <Checkbox v-model:checked="config.footer.note2">尾注2</Checkbox>
                <Input
                  v-model:value="config.footer.note2Text"
                  placeholder="请输入尾注2内容"
                  size="small"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Drawer>
</template>
