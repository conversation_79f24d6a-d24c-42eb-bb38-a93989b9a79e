import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());
import type { TableColumnType } from 'ant-design-vue';

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'ticketName',
      label: '门票名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入门票名称',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'orderNo',
      label: '订单号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入订单号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'verificationCode',
      label: '核销码',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入核销码',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'touristName',
      label: '游客姓名',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入游客姓名',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'orderSource',
      label: '订单来源',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择订单来源',
        allowClear: true,
        options: accessAllEnums.value?.ticketOrderSource.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'payMethod',
      label: '支付方式',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择支付方式',
        allowClear: true,
        options: accessAllEnums.value?.orderPayMethod.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'payStatus',
      label: '付款状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择付款状态',
        allowClear: true,
        options: accessAllEnums.value?.orderPayStatus.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'orderStatus',
      label: '订单状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择订单状态',
        allowClear: true,
        options: accessAllEnums.value?.ticketOrderStatus.list,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'orderDate',
      label: '下单日期',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['下单开始日期', '下单结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
      // defaultValue: [dayjs().startOf('month'), dayjs().endOf('month')], // 默认当月
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      type: 'expand',
      //   field: 'ticketName',
      //   title: '门票信息',
      //   minWidth: 400,
      //   treeNode: true,
      fixed: 'left',
      headerAlign: 'center',
      align: 'left',
      slots: { content: 'expand_content' },
    },
    {
      field: 'ticketName',
      title: '门票信息',
      minWidth: 400,
      //   treeNode: true,
      fixed: 'left',
      headerAlign: 'center',
      align: 'left',
      slots: {
        default: 'expand_header',
      },
    },
    {
      field: 'unitPrice',
      title: '单价',
      width: 120,
      formatter: ({ row }: any) => {
        return row.isParent ? '' : row.unitPrice;
      },
    },
    {
      field: 'ticketNum',
      title: '数量',
      width: 120,
      formatter: ({ row }: any) => {
        return row.isParent ? '' : row.ticketNum;
      },
    },
    {
      field: 'orderTime',
      title: '下单时间',
      width: 180,
      formatter: ({ row }: any) => {
        return row.isParent ? '' : row.orderTime;
      },
    },
    {
      field: 'orderPrice',
      title: '订单金额',
      width: 120,
    },
    {
      field: 'discountPrice',
      title: '优惠金额',
      width: 120,
    },
    {
      field: 'actualPrice',
      title: '实际支付金额',
      width: 120,
    },
    {
      field: 'payStatus',
      title: '支付状态',
      width: 180,
    },
    {
      field: 'orderStatus',
      title: '订单状态',
      width: 180,
    },
    {
      field: 'payMethod',
      title: '支付方式',
      width: 180,
    },
    {
      align: 'center',
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 120,
    },
  ];
}

export function useTableSchema(): TableColumnType[] {
  return [
    {
      title: '操作内容',
      dataIndex: 'actionContent',
      minWidth: 150,
      align: 'center',
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      width: 150,
      customRender: ({ record }: any) => {
        return accessAllEnums.value?.ticketOrderStatus.list.find(
          (item: any) => item.value === record.orderStatus,
        )?.label;
      },
      align: 'center',
    },
    {
      title: '下单人',
      dataIndex: 'userInfo',
      width: 150,
      customRender: ({ record }: any) => {
        return record.userInfo?.name || '--';
      },
      align: 'center',
    },
    {
      title: '游客名称',
      dataIndex: 'touristName',
      width: 180,
      align: 'center',
    },
    {
      title: '操作管理员',
      dataIndex: 'ticketName',
      width: 150,
      align: 'center',
    },
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      width: 180,
      align: 'center',
    },
  ];
}

export function useVerifyTableSchema(): TableColumnType[] {
  return [
    {
      title: '核销景区',
      dataIndex: 'actionContent',
      width: 150,
      align: 'center',
      customRender: ({ record }: any) => {
        return record.scenicInfo?.scenicName || '--';
      },
    },
    {
      title: '核销人',
      dataIndex: 'adminUserInfo',
      width: 120,
      align: 'center',
      customRender: ({ record }: any) => {
        return record.adminUserInfo?.name || '--';
      },
    },
    {
      title: '核销类型',
      dataIndex: 'verificationType',
      width: 120,
      align: 'center',
      customRender: ({ record }: any) => {
        return accessAllEnums.value?.tickeVerificationType.list.find(
          (item: any) => item.value === record.verificationType,
        )?.label;
      },
    },
    {
      title: '核销方式',
      dataIndex: 'verificationMode',
      width: 180,
      align: 'center',
      customRender: ({ record }: any) => {
        return accessAllEnums.value?.tickeVerificationMode.list.find(
          (item: any) => item.value === record.verificationMode,
        )?.label;
      },
    },
    {
      title: '核销次数',
      dataIndex: 'verificationNum',
      width: 120,
      align: 'center',
    },
    {
      title: '核销时间',
      dataIndex: 'verificationTime',
      width: 180,
      align: 'center',
    },
  ];
}

export function useRefundFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'InputNumber',
      fieldName: 'refundPrice',
      label: '退款金额',
      rules: 'required',
      componentProps: {
        placeholder: '请输入退款金额',
        allowClear: true,
      },
    },
    {
      component: 'Textarea',
      fieldName: 'refundReason',
      label: '退款原因',
      componentProps: {
        placeholder: '请输入退款原因',
        allowClear: true,
        rows: 3,
      },
    },
  ];
}
