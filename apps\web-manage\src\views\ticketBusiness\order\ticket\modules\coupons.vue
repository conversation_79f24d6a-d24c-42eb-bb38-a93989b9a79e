<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { Table, Card } from 'ant-design-vue';
import { useTableSchema } from '../data';
import { getOrderLog } from '#/api';
const props = defineProps(['orderId']);
const orderLog = ref([]);
const total = ref(0);
const params = ref({
  orderId: props.orderId,
  page: 1,
  pageSize: 10,
});
const getOrderLogList = async () => {
  const res = await getOrderLog(params.value);
  orderLog.value = res.list;
  total.value = res.total;
  console.log(res);
};
onMounted(() => {
  getOrderLogList();
});
</script>
<template>
  <Card title="优惠券使用详情">
    <Table
      :columns="useTableSchema()"
      :dataSource="orderLog"
      :pagination="{
        current: params.page,
        pageSize: params.pageSize,
        total: total,
        onChange: (page, pageSize) => {
          params.page = page;
          params.pageSize = pageSize;
          getOrderLogList();
        },
        showTotal: (total) => `共 ${total} 条`,
      }"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex == 'touristName'">
          <p v-if="record.touristName">
            {{ record.touristName }}<br />{{ record.touristIdcard }}
          </p>
          <p v-else>--</p>
        </template>
        <template v-if="column.dataIndex == 'ticketName'">
          <p v-if="record.adminUserInfo">
            {{ record.adminUserInfo?.name }}<br />{{
              record.adminUserInfo?.phone
            }}
          </p>
          <p v-else>--</p>
        </template>
      </template>
    </Table>
  </Card>
</template>
