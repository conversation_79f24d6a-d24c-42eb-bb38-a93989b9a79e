import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

async function getInsiderList(params: Recordable<any>) {
  return requestClient.get('/sys/insider/list', { params });
}
async function getAllInsiderList(params: Recordable<any>) {
  return requestClient.get('/sys/insider/all', { params });
}

async function getInsiderInfo(id: string) {
  return requestClient.get('/sys/insider/info', { params: { id } });
}
async function createInsider(data: Recordable<any>) {
  return requestClient.post('/sys/insider/create', data);
}

async function updateInsider(data: Recordable<any>) {
  return requestClient.post('/sys/insider/update', data);
}

async function deleteInsider(id: string) {
  return requestClient.post('/sys/insider/delete', { id });
}

async function changeInsiderStatus(data: Recordable<any>) {
  return requestClient.post('/sys/insider/changeStatus', data);
}

export {
  getInsider<PERSON>ist,
  getAllInsiderList,
  getInsiderInfo,
  createInsider,
  updateInsider,
  deleteInsider,
  changeInsiderStatus,
};
