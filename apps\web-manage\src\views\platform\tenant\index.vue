<script setup lang="ts">
import type { Recordable } from '@vben/types';

import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { Page, useVbenModal, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message, Modal, Switch } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useColumns, useGridFormSchema } from './data';
import { getTenantList, deleteTenant, initTenant } from '#/api';

import Form from './modules/form.vue';
import Permission from './modules/permission.vue';

const onActionClick = ({ code, row }: { code: string; row: any }) => {
  if (code === 'edit') {
    onEdit(row);
  } else if (code === 'delete') {
    onDelete(row);
  } else if (code === 'reset') {
    onInit(row);
  } else if (code === 'permission') {
    onPermission(row);
  }
};

const onCreate = () => {
  formModelApi.setData({}).open();
};
const onEdit = (row: any) => {
  formModelApi.setData(row).open();
};
const onDelete = async (row: any) => {
  const hideLoading = message.loading({
    content: '删除中...',
    duration: 0,
    key: 'action_process_msg',
  });
  deleteTenant(row.id)
    .then(() => {
      message.success('删除成功');
      onRefresh();
      hideLoading();
    })
    .catch(() => {
      hideLoading();
    });
};
const onInit = async (row: any) => {
  try {
    await confirm(`你要将企业【${row.tenantName}】初始化吗？`, `企业初始化`);
    await initTenant(row.id);
    message.success('初始化成功');
    return true;
  } catch {
    return false;
  }
};

const onPermission = async (row: any) => {
  permissionFormDrawerApi.setData(row).open();
};

const onRefresh = () => {
  gridApi.query();
};

const [FormModel, formModelApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});
const [PermissionFormDrawer, permissionFormDrawerApi] = useVbenDrawer({
  connectedComponent: Permission,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    headerCellConfig: {
      height: 40,
    },
    cellConfig: {
      height: 70,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const res: any = await getTenantList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

const confirm = (content: string, title: string) => {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
};
</script>
<template>
  <Page auto-content-height>
    <FormModel @success="onRefresh"></FormModel>
    <PermissionFormDrawer @success="onRefresh"></PermissionFormDrawer>
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          新增企业
        </Button>
      </template>
    </Grid>
  </Page>
</template>
