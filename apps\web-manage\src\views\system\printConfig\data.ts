import { toRefs } from 'vue';
import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';

const { accessAllEnums } = toRefs(useAccessStore());
// 搜索表单
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '模版名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入模版名称',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'templateType',
      label: '模版类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择模版类型',
        allowClear: true,
        options: accessAllEnums.value?.printTemplateType.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: accessAllEnums.value?.status.list,
      },
    },
  ];
}

// 表单组件
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'templateName',
      componentProps: {
        placeholder: '请输入模版名称',
        allowClear: true,
      },
      label: '模版名称',
      rules: 'required',
    },
    {
      component: 'Select',
      fieldName: 'templateType',
      label: '模版类型',
      rules: 'required',
      componentProps: {
        placeholder: '请选择模版类型',
        allowClear: true,
        options: accessAllEnums.value?.printTemplateType.list,
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '不启用', value: 0 },
        ],
        optionType: 'default',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'InputNumber',
      fieldName: 'listorder',
      label: '排序',
      componentProps: {
        placeholder: '请输入排序',
        allowClear: true,
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        allowClear: true,
      },
    },
  ];
}
// 表格组件
export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'templateName',
      title: '模版名称',
      width: 200,
    },
    {
      field: 'templateType',
      title: '模版类型',
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.printTemplateType.list.find(
          (item: any) => item.value === row.templateType,
        )?.label;
      },
      width: 120,
    },

    {
      field: 'createdAt',
      title: '创建时间',
      width: 180,
    },
    {
      field: 'status',
      width: 120,
      title: '状态',
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: onStatusChange,
        },
      },
    },
    {
      field: 'remark',
      title: '备注',
      align: 'left',
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'templateName',
          nameTitle: '模版名称',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'config',
            text: '配置',
          },
          'edit', // 默认的编辑按钮
          {
            code: 'delete', // 默认的删除按钮
            show: (row: any) => {
              return (row.isSys !== 1);
            },
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 150,
    },
  ];
}
