import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { getAllScenicList } from '#/api';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '设备名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入设备名称',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'deviceSn',
      label: '设备SN',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入设备SN',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: accessAllEnums.value?.status.list,
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'deviceName',
      title: '自助售票机名称',
      width: 200,
      fixed: 'left',
    },
    {
      field: 'scenicName',
      title: '所属景区',
      width: 150,
      formatter: ({ row }: any) => {
        return row.scenicInfo.scenicName;
      },
    },
    {
      field: 'deviceMac',
      title: 'MAC地址',
      width: 150,
    },
    {
      field: 'deviceSn',
      title: '设备SN',
      width: 200,
    },
    {
      field: 'ip',
      title: 'IP地址',
      width: 150,
    },
    {
      field: 'status',
      width: 120,
      title: '状态',
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: onStatusChange,
        },
      },
    },
    {
      field: 'deviceModel',
      title: '设备型号',
      width: 150,
    },
    {
      field: 'manufacturer',
      title: '制造商',
      width: 150,
    },
    {
      field: 'address',
      title: '安装位置',
      width: 150,
    },
    {
      field: 'createdBy',
      title: '创建人',
      width: 150,
    },
    {
      field: 'operation',
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'deviceName',
          nameTitle: '自助售票机',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'order',
            text: '售票记录',
          },
          'edit',
          'delete',
        ],
      },
      fixed: 'right',
      title: '操作',
      width: 180,
    },
  ];
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'deviceName',
      label: '设备名称',
      componentProps: {
        placeholder: '请输入设备名称',
        allowClear: true,
      },
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'deviceSn',
      label: '设备序列号',
      componentProps: {
        placeholder: '请输入设备序列号',
        allowClear: true,
      },
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'deviceMac',
      label: 'MAC地址',
      componentProps: {
        placeholder: '请输入MAC地址',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'ip',
      label: 'IP地址',
      componentProps: {
        placeholder: '请输入IP地址',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'deviceModel',
      label: '设备型号',
      componentProps: {
        placeholder: '请输入设备型号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'manufacturer',
      label: '设备制造商',
      componentProps: {
        placeholder: '请输入设备制造商',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'address',
      label: '安装位置',
      componentProps: {
        placeholder: '请输入安装位置',
        allowClear: true,
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        allowClear: true,
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '不启用', value: 0 },
        ],
        optionType: 'default',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
      rules: 'selectRequired',
    },
  ];
}

export function useGridFormOrderLogSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'ticketName',
      label: '门票名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入门票名称',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'orderNo',
      label: '订单编号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入订单编号',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'orderType',
      label: '订单类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择订单类型',
        allowClear: true,
        options: accessAllEnums.value?.ticketOrderType.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'payMethod',
      label: '支付方式',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择支付方式',
        allowClear: true,
        options: accessAllEnums.value?.orderPayMethod.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'payStatus',
      label: '付款状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择付款状态',
        allowClear: true,
        options: accessAllEnums.value?.orderPayStatus.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'orderStatus',
      label: '订单状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择订单状态',
        allowClear: true,
        options: accessAllEnums.value?.ticketOrderStatus.list,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'orderDate',
      label: '下单日期',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['下单开始日期', '下单结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}
export function useOrderLogColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'orderNo',
      title: '订单号',
      width: 150,
    },
    {
      field: 'userName',
      title: '下单人',
      width: 150,
    },
    {
      field: 'userPhone',
      title: '联系电话',
      width: 150,
    },
    {
      field: 'userIdcard',
      title: '身份证',
      width: 150,
    },
    {
      field: 'orderTime',
      title: '下单时间',
      width: 150,
    },
    {
      field: 'orderPrice',
      title: '下单金额',
      width: 150,
    },
    {
      field: 'remark',
      title: '备注',
      width: 150,
    },
  ];
}
