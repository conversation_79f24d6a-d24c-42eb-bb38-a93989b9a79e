import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'couponNo',
      label: '优惠券编号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入优惠券编号',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'couponType',
      label: '优惠券类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择优惠券类型',
        options: [
          { label: '满减劵', value: 1 },
          { label: '折扣券', value: 2 },
        ],
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'orderNo',
      label: '订单编号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入订单编号',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'useStatus',
      label: '使用状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择使用状态',
        options: accessAllEnums.value.couponUseStatus.list,
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'getType',
      label: '获取方式',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择获取方式',
        options: [
          { label: '领取', value: 1 },
          { label: '发放', value: 2 },
        ],
        allowClear: true,
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'userName',
      title: '用户名',
      minWidth: 150,
      formatter: ({ row }: any) => {
        return row.userInfo.name;
      },
    },
    {
      field: 'userPhone',
      title: '手机号',
      minWidth: 150,
      formatter: ({ row }: any) => {
        return row.userInfo.phone;
      },
    },
    {
      field: 'couponName',
      title: '优惠券名称',
      minWidth: 150,
      formatter: ({ row }: any) => {
        return row.couponInfo.couponName;
      },
    },
    {
      field: 'couponType',
      title: '优惠券类型',
      minWidth: 150,
      formatter: ({ row }: any) => {
        return row.couponType === 1 ? '满减劵' : '折扣券';
      },
    },
    {
      field: 'validDate',
      title: '有效期',
      width: 180,
      formatter: ({ row }: any) => {
        return row.validType === 1
          ? '永久有效'
          : row.validType === 2
            ? row.validBeginDate + '~' + row.validEndDate
            : '领取后' + row.validDayNum + '天内有效';
      },
    },
    {
      field: 'getType',
      title: '获取方式',
      width: 180,
      formatter: ({ row }: any) => {
        return row.getType === 1 ? '领取' : '发放';
      },
    },
    {
      field: 'getTime',
      title: '领取时间',
      width: 150,
    },
    {
      field: 'useStatus',
      title: '使用状态',
      width: 150,
      cellRender: {
        name: 'CellTag',
        options: accessAllEnums.value?.couponUseStatus.list.map((item: any) => {
          return {
            value: item.value,
            label: item.label,
            color: type[item.value],
          };
        }),
      },
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.couponUseStatus.list.find(
          (item: any) => item.value === row.useStatus,
        )?.label;
      },
    },
    {
      field: 'useTime',
      title: '使用时间',
      width: 150,
    },
    {
      field: 'consumeType',
      title: '消费类型',
      width: 150,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.prepaidCardConsumeType.list.find(
          (item: any) => item.value === row.consumeType,
        )?.label;
      },
    },
    {
      field: 'orderNo',
      title: '消费订单号',
      width: 150,
    },
    {
      field: 'operation',
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'couponName',
          nameTitle: '优惠券',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'revoke',
            text: '作废',
            danger: true,
            show: (row: any) => row.useStatus !== 3,
          },
        ],
      },
      fixed: 'right',
      title: '操作',
      width: 120,
    },
  ];
}

const type: any = {
  0: 'warning',
  1: 'success',
  2: 'error',
  3: 'default',
};
