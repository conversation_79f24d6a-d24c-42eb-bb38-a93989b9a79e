<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import { computed, ref, h, toRefs } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { useVbenForm } from '#/adapter/form';
import { Button, Radio, RadioGroup } from 'ant-design-vue';
import { createSpot, updateSpot } from '#/api';
import CusUpload from '#/components/CusUpload/index.vue';
import { useFormSchema } from '../data';
import Map from './map.vue';
const emits = defineEmits(['success']);

const formData = ref<any>();

// 表单配置
const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    labelWidth: 120,
    componentProps: {
      class: 'w-full',
    },
  },
  schema: useFormSchema(),
  showDefaultActions: false,
  layout: 'horizontal',
  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2',
});

// 表单弹窗
const id = ref();
const [Model, modelApi] = useVbenModal({
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modelApi.getData<any>();
      formApi.resetForm();
      if (data) {
        // 创建新对象而不是直接赋值
        const newFormData = { ...data };

        if (data.spotCover) {
          newFormData.spotCover = data.spotCover
            .split(',')
            .map((item: any) => {
              return {
                uid: item,
                name: item,
                status: 'done',
                url: item,
                response: {
                  url: item,
                },
              };
            });
        } else {
          newFormData.spotCover = [];
        }
        if (data.spotImage) {
          newFormData.spotImage = data.spotImage
            .split(',')
            .map((item: any) => {
              return {
                uid: item,
                name: item,
                status: 'done',
                url: item,
                response: {
                  url: item,
                },
              };
            });
        } else {
          newFormData.spotImage = [];
        }
        if (data.spotAudio) {
          newFormData.spotAudio = data.spotAudio
            .split(',')
            .map((item: any) => {
              return {
                uid: item,
                name: item,
                status: 'done',
                url: item,
                response: {
                  url: item,
                },
              };
            });
        } else {
          newFormData.spotAudio = [];
        }
        // 赋值给formData.value
        formData.value = newFormData;
        id.value = data.id;
        formApi.setValues(formData.value);
      } else {
        id.value = undefined;
      }
    }
  },
});

// 地图弹窗
const showMap = async () => {
  const values = await formApi.getValues();
  mapModalApi.setData({ ...values }).open();
};
const [MapModal, mapModalApi] = useVbenModal({
  connectedComponent: Map,
  destroyOnClose: false,
});

// 地图弹窗确认
const mapConfirm = async (mapData: any) => {
  console.log('获取定位', mapData);
  const values = await formApi.getValues();
  formApi.setValues({
    ...values,
    spotAddress: mapData.spotAddress,
    latitude: mapData.latitude,
    longitude: mapData.longitude,
  });
};

// 处理表单数据，返回处理后的值
const processFormValues = async () => {
  const { valid } = await formApi.validate();
  if (!valid) return null;

  const values = await formApi.getValues();
  console.log(values, 'values');
  // 处理图片和音频字段
  ['spotCover', 'spotImage', 'spotAudio'].forEach((field) => {
    if (values[field]) {
      values[field] = values[field]
        .map((item: any) => item.url)
        .join(',');
    }
  });

  return values;
};

// 统一的提交处理函数
const handleSubmit = async (resetAfterSuccess = false) => {
  const values = await processFormValues();
  if (!values) return;
  values.businessHours = values.businessHours.join(' ~ ');
  modelApi.lock();
  try {
    if (id.value) {
      await updateSpot({ id: id.value, ...values });
    } else {
      await createSpot({ ...values });
    }

    emits('success');

    if (resetAfterSuccess) {
      formApi.resetForm();
      modelApi.unlock();
    } else {
      modelApi.close();
    }
  } catch (error) {
    modelApi.unlock();
  }
};

const submitOnEnter = () => handleSubmit();

const getModelTitle = computed(() => {
  return formData.value?.id ? '编辑景点' : '新增景点';
});
</script>
<template>
  <Model class="w-[1200px]" :title="getModelTitle">
    <Form>
      <template #positioning="slotProps">
        <RadioGroup v-bind="slotProps">
          <Radio :value="1">启用</Radio>
          <Radio :value="0">不启用</Radio>
        </RadioGroup>
        <Button size="small" @click="showMap" v-if="slotProps.value === 1">
          <template #icon>
            <IconifyIcon icon="mdi:map-marker" class="size-4" />
          </template>
          获取定位
        </Button>
      </template>
      <template #spotCover="slotProps">
        <div>
          <CusUpload v-bind="slotProps" list-type="picture-card">
            <IconifyIcon icon="mdi:upload" class="size-4" />
          </CusUpload>
          <!-- <p class="flex items-center text-[12px] text-gray-500">
            <IconifyIcon icon="mdi:info" class="mr-1 size-4" />
            建议图片尺寸不小于375X211px，图片比例16:9，不超过9张
          </p> -->
        </div>
      </template>
      <template #spotImage="slotProps">
        <div>
          <CusUpload v-bind="slotProps" list-type="picture-card">
            <IconifyIcon icon="mdi:upload" class="size-4" />
          </CusUpload>
          <p class="mt-2 flex items-center text-[12px] text-gray-500">
            <IconifyIcon icon="mdi:info" class="mr-1 size-4" />
            建议图片尺寸不小于375X211px，图片比例16:9，不超过9张
          </p>
        </div>
      </template>
      <template #spotAudio="slotProps">
        <div>
          <CusUpload v-bind="slotProps" list-type="text" :maxFileSize="50">
            <Button>
              <IconifyIcon icon="mdi:upload" class="size-4" />
              上传
            </Button>
          </CusUpload>
        </div>
      </template>
    </Form>
    <template #footer v-if="!formData?.id">
      <Button type="default" @click="modelApi.close()">取消</Button>
      <Button type="primary" @click="submitOnEnter">保存</Button>
    </template>
  </Model>
  <MapModal @confirm="mapConfirm" />
</template>
<style lang="scss" scoped></style>
