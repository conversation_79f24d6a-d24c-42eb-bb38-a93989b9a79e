<script setup lang="ts">
// 默认参数 =======================================================================================
import { ref } from 'vue';

import selectTem from '../components/select.vue';

// ================================================================================================

// 默认参数（必填项：tabUrl, labelFieldKey, keyField, columns）
const infoVal = ref({
  tabUrl: '/user/userGetPaging',
  labelFieldKey: 'label',
  keyField: 'value',
  selectTitle: '员工',
  defaultSearch: {
    // 默认搜索条件
    isInTheJob: true,
    roleId: null,
  },

  // 表格列
  columns: [
    { field: 'name', title: '员工名称' },
    { field: 'phone', title: '手机号码' },
    { field: 'isTeacherDesc', title: '是否授课' },
    { field: 'roleName', title: '角色' },
    { field: 'remark', title: '备注' },
  ],

  // 搜索条件
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '姓名/手机号码',
      },
      fieldName: 'key',
    },
  ],
});
</script>

<template>
  <selectTem :info-val="infoVal" v-bind="$attrs" />
</template>

<style lang="scss" scoped></style>
