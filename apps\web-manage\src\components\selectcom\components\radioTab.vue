<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { customReq } from '#/api/config';

const props = defineProps({
  apiUrl: { type: String, default: '' },
  columns: { type: Array<any>, default: () => [] },
  keyField: { type: String, default: 'value' },
  checkRowKeys: { type: Array<any>, default: () => [] },
  labelFieldKey: { type: String, default: 'label' },
  defaultSearch: { type: Object, default: () => {} },
});
const emits = defineEmits(['radioSelect']);
const tableData: any = ref([]);

// =======================================================================================
const clearRadioRow = () => {
  gridApi.grid.clearRadioRow();
};
const setRadioRow = (row: any) => {
  gridApi.grid.setRadioRow(row);
};

const searchParams: any = ref({});
const search = (e: any) => {
  searchParams.value = e;
  gridApi.query();
};

defineExpose({
  clearRadioRow,
  setRadioRow,
  search,
});

// =======================================================================================
const gridOptions: VxeGridProps<any> = {
  columns: props.columns,
  rowConfig: {
    keyField: props.keyField,
  },
  columnConfig: {
    resizable: false,
  },
  radioConfig: {
    // highlight: true,
    labelField: props.labelFieldKey,
    checkRowKey: props.checkRowKeys[0] || '',
    reserve: true,
  },
  exportConfig: {},
  height: '500',
  keepSource: true,
  align: 'left',
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        const getParams: any = {
          pageCurrent: page.currentPage,
          pageSize: page.pageSize,
          ...props.defaultSearch,
          ...searchParams.value,
        };

        const resData = await customReq(props.apiUrl, getParams);
        tableData.value = resData.dataItems;
        return {
          items: resData.dataItems,
          total: resData.recordCount,
        };
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    // import: true,
    // refresh: { code: 'query' },
    zoom: false,
  },
};

const gridEvents: any = {
  // 单选
  radioChange: ({ row }: any) => {
    emits('radioSelect', row);
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});
</script>

<template>
  <Grid />
</template>
