<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { computed, ref, h, toRefs } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Form, FormItem, Button, TimeRangePicker, Tag } from 'ant-design-vue';

const emit = defineEmits(['confirm']);
const [ModelTime, modelTimeApi] = useVbenModal({
  title: '分时预约设置',
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    let data = modelTimeApi.getData();
    if (isOpen) {
      timeData.value = data;
    }
  },
});
const formData = ref<Recordable<any>>({});
const timeData = ref<Recordable<any>>([]);
const timeValue = ref<any>(null);
const handleSetTime = (type: 'am' | 'hour') => {
  console.log(type);
  if (type === 'am') {
    timeData.value = [
      {
        beginTime: '00:00',
        endTime: '11:59',
        periodStock: 0,
        periodPrice: 0,
      },
      {
        beginTime: '12:00',
        endTime: '23:59',
        periodStock: 0,
        periodPrice: 0,
      },
    ];
  } else {
    createTimeData();
  }
};
const handleAddTime = () => {
  console.log('添加');
  if (timeValue.value) {
    timeData.value.push({
      beginTime: timeValue.value[0],
      endTime: timeValue.value[1],
      periodStock: 0,
      periodPrice: 0,
    });
  }
};
const handleClear = () => {
  console.log('清空');
  timeData.value = [];
};
const handleTimeClose = (item: any) => {
  console.log(item);
  timeData.value = timeData.value.filter((items: any) => {
    return items.beginTime !== item.beginTime;
  });
};

const handleSubmit = () => {
  emit('confirm', timeData.value);
  modelTimeApi.close();
};
const createTimeData = () => {
  let timeSlots = [];
  for (let hour = 0; hour < 24; hour++) {
    const hh = hour < 10 ? `0${hour}` : `${hour}`;
    timeSlots.push([`${hh}:00`, `${hh}:59`]);
  }
  timeData.value = timeSlots.map(([beginTime, endTime]) => {
    return {
      beginTime,
      endTime,
      periodStock: '',
      periodPrice: -1,
    };
  });
};
</script>

<template>
  <ModelTime>
    <Form
      :model="formData"
      :labelCol="{
        style: {
          fontSize: '14px',
          lineHeight: '22px',
          fontWeight: '500',
          textAlign: 'right',
          marginRight: '8px',
          width: '100px',
        },
      }"
    >
      <FormItem label="一键设置" :colon="false">
        <Button type="primary" @click="handleSetTime('am')">上下午</Button>
        <Button type="primary" class="mx-2" @click="handleSetTime('hour')"
          >整点时分</Button
        >
        <Button type="primary" danger @click="handleClear">清空</Button>
      </FormItem>
      <FormItem label="分段设置" :colon="false">
        <TimeRangePicker
          format="HH:mm"
          value-format="HH:mm"
          v-model:value="timeValue"
        />
        <Button type="primary" class="ml-2" @click="handleAddTime">确定</Button>
      </FormItem>
      <FormItem label="时段预览" :colon="false">
        <div
          class="flex min-h-[40px] flex-wrap items-center gap-2 rounded-md border border-gray-300 p-2"
        >
          <div v-for="item in timeData" :key="item.beginTime">
            <Tag closable color="blue" @close="handleTimeClose(item)"
              >{{ item.beginTime }} - {{ item.endTime }}</Tag
            >
          </div>
        </div>
      </FormItem>
    </Form>
  </ModelTime>
</template>
