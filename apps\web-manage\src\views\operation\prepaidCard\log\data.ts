import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'cardNo',
      label: '卡号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入卡号',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'orderNo',
      label: '订单号',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入订单号',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'tradeType',
      label: '交易类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择交易类型',
        allowClear: true,
        options: accessAllEnums.value?.prepaidCardTradeType.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'consumeType',
      label: '消费类型',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择消费类型',
        allowClear: true,
        options: accessAllEnums.value?.prepaidCardConsumeType.list,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'dateRange',
      label: '交易时间',
      hideLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['交易开始日期', '交易结束日期'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}

export function useColumns<T = any>(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'cardNo',
      title: '卡号',
      minWidth: 150,
      slots: { default: 'cardNo' },
    },
    {
      field: 'cardType',
      title: '类型',
      width: 100,
      slots: { default: 'cardType' },
    },
    {
      field: 'name',
      title: '姓名',
      width: 120,
      slots: { default: 'name' },
    },
    {
      field: 'tradeType',
      title: '交易类型',
      width: 120,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.prepaidCardTradeType.list.find(
          (item: any) => item.value === row.tradeType,
        )?.label;
      },
    },
    {
      field: 'beforeAmount',
      title: '交易前余额',
      width: 120,
    },
    {
      field: 'amount',
      title: '交易金额',
      width: 150,
      slots: { default: 'amount' },
    },
    {
      field: 'afterAmount',
      title: '交易后余额',
      width: 120,
    },
    {
      field: 'consumeType',
      title: '消费类型',
      width: 120,
      formatter: ({ row }: any) => {
        return accessAllEnums.value?.prepaidCardConsumeType.list.find(
          (item: any) => item.value === row.consumeType,
        )?.label;
      },
    },
    {
      field: 'orderNo',
      title: '消费订单',
      width: 200,
    },
    {
      field: 'createdAt',
      title: '交易时间',
      minWidth: 200,
    },
  ];
}
