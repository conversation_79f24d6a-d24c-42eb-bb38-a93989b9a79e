import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    tokenType: string;
    accessToken: string;
    expiresIn: number;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<AuthApi.LoginResult>(
    '/sys/adminUser/login',
    data,
  );
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return baseRequestClient.post<AuthApi.RefreshTokenResult>('/auth/refresh', {
    withCredentials: true,
  });
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return requestClient.post('/sys/adminUser/logout');
}

/**
 * 获取用户权限
 */
export async function getPermissions() {
  return requestClient.get<any>('/sys/adminUser/permissions');
}

/**
 * 获取所有枚举数据
 */
export async function getAllEnums() {
  return requestClient.get<any>('/sys/common/getAllEnums');
}
