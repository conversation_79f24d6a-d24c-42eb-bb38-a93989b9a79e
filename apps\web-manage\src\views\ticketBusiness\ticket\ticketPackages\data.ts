import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import { useAccessStore } from '@vben/stores';
import { toRefs, markRaw } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());
import {
  getAllScenicList,
  getTicketTypeAllList,
  getHolidayAllList,
} from '#/api';
import Fee from './modules/formItem/fee.vue';
import Reserve from './modules/formItem/reserve.vue';
import PurchaseDate from './modules/formItem/purchaseDate.vue';
import PurchaseTime from './modules/formItem/purchaseTime.vue';
import WriteOffT from './modules/formItem/writeOffT.vue';
import { z } from '#/adapter/form';
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: '套票名称',
      hideLabel: true,
      componentProps: {
        placeholder: '请输入套票名称',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'typeId',
      label: '所属分类',
      hideLabel: true,
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.typeName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getTicketTypeAllList,
        placeholder: '请选择所属分类',
        alwaysLoad: true, //每次`visibleEvent`事件发生时都重新请求数据
      },
      dependencies: {
        componentProps(values) {
          if (values.scenicId) {
            return {
              params: {
                scenicId: values.scenicId,
              },
            };
          }
          return {};
        },
        triggerFields: ['scenicId'],
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        options: accessAllEnums.value?.status.list,
      },
    },
    {
      component: 'Select',
      fieldName: 'saleResource',
      label: '可售渠道',
      hideLabel: true,
      componentProps: {
        placeholder: '请选择可售渠道',
        allowClear: true,
        options: accessAllEnums.value?.ticketSaleResource.list,
      },
    },
  ];
}

export function useColumns<T = any>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: 'ID',
      width: 100,
    },
    {
      field: 'ticketCover',
      title: '封面',
      width: 80,
      cellRender: {
        name: 'CellImage',
        attrs: {
          width: 60,
          height: 60,
        },
      },
    },
    {
      field: 'ticketName',
      title: '套票名称',
      width: 200,
    },
    {
      field: 'ticketTypeInfo',
      title: '类别',
      width: 150,
      formatter: ({ row }) => {
        return row.ticketTypeInfo?.typeName;
      },
    },
    {
      field: 'scenicInfo',
      title: '所属景区',
      width: 150,
      formatter: ({ row }) => {
        return row.scenicInfo?.scenicName;
      },
    },
    {
      field: 'sellingPrice',
      title: '销售价',
      width: 100,
    },
    {
      field: 'marketPrice',
      title: '市场价',
      width: 100,
    },

    {
      field: 'listorder',
      title: '排序',
      width: 100,
    },
    {
      field: 'status',
      width: 120,
      title: '状态',
      cellRender: {
        name: 'CellSwitch',
        attrs: {
          beforeChange: onStatusChange,
        },
      },
    },
    {
      field: 'saleResource',
      title: '可售渠道',
      align: 'left',
      width: 280,
      slots: {
        default: 'CellSaleResource',
      },
    },
    {
      field: 'ticketTags',
      title: '标签',
      align: 'left',
      minWidth: 220,
      slots: {
        default: 'CellTags',
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'ticketName',
          nameTitle: '套票',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'info',
            text: '查看',
          },
          {
            code: 'copy',
            text: '复制',
          },
          'edit', // 默认的编辑按钮
          'delete', // 默认的删除按钮
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 200,
    },
  ];
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Divider',
      fieldName: 'divider1',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-0',
      hideLabel: true,
      renderComponentContent() {
        return {
          default: () => '基本信息',
        };
      },
    },
    {
      component: 'Input',
      fieldName: 'ticketName',
      label: '门票名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入门票名称',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'scenicId',
      label: '所属景区',
      rules: 'selectRequired',
      componentProps: {
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.scenicName,
            value: item.id,
          }));
        },
        api: getAllScenicList,
        placeholder: '请选择所属景区',
        allowClear: true,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'typeId',
      label: '所属分类',
      rules: 'selectRequired',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.typeName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getTicketTypeAllList,
        placeholder: '请选择所属分类',
        alwaysLoad: true,
      },
      dependencies: {
        componentProps(values) {
          if (values.scenicId) {
            return {
              params: {
                scenicId: values.scenicId,
              },
            };
          }
          return {};
        },
        if: ({ scenicId }) => scenicId,
        triggerFields: ['scenicId'],
      },
    },
    {
      component: 'TimeRangePicker',
      fieldName: 'entryTime',
      label: '入园时间',
      rules: 'selectRequired',
      componentProps: {
        format: 'HH:mm',
        allowClear: true,
        placeholder: ['开始时间', '结束时间'],
        separator: '至',
        valueFormat: 'HH:mm',
      },
    },
    {
      component: 'TagInput',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-6',
      fieldName: 'ticketTags',
      componentProps: {
        class: 'w-full',
        separator: ',',
        placeholder: '请输入标签',
        maxTags: 5,
      },
      label: '标签',
    },
    {
      component: 'Input',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-6',
      fieldName: 'entryAddress',
      label: '入园地址',
      componentProps: {
        placeholder: '请输入入园地址',
        allowClear: true,
      },
    },
    {
      component: 'CusUpload',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-6',
      componentProps: {
        maxCount: 1,
        accept: '.jpg,.png,.jpeg,.bmp,.gif,.webp',
        fileTypeTag: 'ticket',
        multiple: false,
        showUploadList: true,
        listType: 'picture-card',
      },
      fieldName: 'ticketCover',
      label: '门票封面',
      rules: z
        .array(z.object({ url: z.string().url() }))
        .min(1, '请上传门票封面'),
    },
    {
      component: 'CusUpload',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-6',
      fieldName: 'ticketImg',
      label: '门票图片',
      componentProps: {
        maxCount: 9,
        accept: '.jpg,.png,.jpeg,.bmp,.gif,.webp',
        fileTypeTag: 'ticket',
        multiple: false,
        showUploadList: true,
        listType: 'picture-card',
      },
      rules: z
        .array(z.object({ url: z.string().url() }))
        .min(1, '请上传门票图片'),
    },
    {
      component: 'TextEditor',
      fieldName: 'ticketDesc',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '门票描述',
      componentProps: {
        class: 'w-full',
        height: '300px',
        fileTypeTag: 'ticket',
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '启用', value: 1 },
          { label: '不启用', value: 0 },
        ],
        optionType: 'default',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'InputNumber',
      fieldName: 'listorder',
      label: '排序',
      componentProps: {
        placeholder: '请输入排序',
        allowClear: true,
        precision: 0,
      },
      defaultValue: 0,
    },
    {
      component: 'Divider',
      fieldName: 'divider2',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-0',
      hideLabel: true,
      renderComponentContent() {
        return {
          default: () => '子票配置',
        };
      },
    },
    {
      component: 'Input',
      fieldName: 'childList',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '',
      hideLabel: true,
    },
    {
      component: 'Divider',
      fieldName: 'divider2',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-0',
      hideLabel: true,
      renderComponentContent() {
        return {
          default: () => '价格库存',
        };
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'sellingPrice',
      rules: 'required',
      label: '销售价',
      componentProps: {
        placeholder: '请输入销售价',
        allowClear: true,
        precision: 2,
        min: 0,
      },
      suffix: '元',
    },
    {
      component: 'InputNumber',
      fieldName: 'marketPrice',
      label: '市场价',
      componentProps: {
        placeholder: '请输入市场价',
        allowClear: true,
        precision: 2,
        min: 0,
      },
      suffix: '元',
    },
    {
      component: 'InputNumber',
      fieldName: 'dayStock',
      label: '日库存',
      componentProps: {
        placeholder: '请输入日库存',
        allowClear: true,
        precision: 0,
      },
      defaultValue: -1,
      help: '-1表示不限制',
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'saleResource',
      label: '可售渠道',
      rules: 'selectRequired',
      componentProps: {
        options: accessAllEnums.value?.ticketSaleResource.list,
      },
    },
    {
      component: 'Switch',
      fieldName: 'isModifyPrice',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '可修改价格',
      rules: 'required',
      defaultValue: 0,
      help: '开启后，允许窗口/手持机售票修改价格',
      componentProps: {
        class: 'w-[45px]',
        checkedValue: 1,
        uncheckedValue: 0,
      },
    },
    {
      component: 'Divider',
      fieldName: 'divider2',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-0',
      hideLabel: true,
      renderComponentContent() {
        return {
          default: () => '购票规则',
        };
      },
    },
    {
      component: markRaw(Reserve),
      fieldName: 'reserve',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '是否可预约',
      rules: 'required',
      defaultValue: [0, 1],
      disabledOnChangeListener: false,
    },
    {
      component: markRaw(PurchaseDate),
      fieldName: 'purchaseD',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '可购买日期',
      rules: 'required',
      defaultValue: [0, []],
      disabledOnChangeListener: false,
    },
    {
      component: markRaw(PurchaseTime),
      fieldName: 'purchaseT',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '可购买时间',
      rules: 'required',
      defaultValue: [0, []],
      disabledOnChangeListener: false,
    },
    {
      component: 'Switch',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      fieldName: 'isReservation',
      label: '开启分时预约',
      rules: 'required',
      defaultValue: 0,
      componentProps: {
        class: 'w-[45px]',
        checkedValue: 1,
        uncheckedValue: 0,
      },
      dependencies: {
        show: false,
        triggerFields: ['purchaseD'],
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '限制', value: 1 },
          { label: '不限制', value: 0 },
        ],
        optionType: 'default',
      },
      defaultValue: 0,
      fieldName: 'isOrderQuantity',
      label: '限制下单数量',
      rules: 'selectRequired',
    },
    {
      component: 'InputNumber',
      fieldName: 'orderQuantity',
      label: '下单数量限制',
      dependencies: {
        show: ({ isOrderQuantity }) => isOrderQuantity === 1,
        triggerFields: ['isOrderQuantity'],
      },
      componentProps: {
        placeholder: '请输入下单数量限制',
        allowClear: true,
        precision: 0,
        min: 1,
      },
      defaultValue: 1,
    },
    {
      component: 'Select',
      fieldName: 'authenticationType',
      label: '实名制类型',
      componentProps: {
        placeholder: '请选择实名制类型',
        allowClear: true,
        options: accessAllEnums.value?.tickeAuthenticationType.list,
      },
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      fieldName: 'qrcodeRule',
      label: '二维码规则',
      componentProps: {
        placeholder: '请选择二维码规则',
        allowClear: true,
        options: accessAllEnums.value?.tickeQrcodeRule.list,
      },
      rules: 'selectRequired',
      dependencies: {
        componentProps(values, formApi) {
          if (values.authenticationType === 3) {
            formApi.setFieldValue('qrcodeRule', 1);
            return {
              options: accessAllEnums.value?.tickeQrcodeRule.list.map(
                (item: any) => {
                  if (item.value === 2) {
                    return {
                      label: item.label,
                      value: item.value,
                      disabled: true,
                    };
                  }
                  return {
                    label: item.label,
                    value: item.value,
                  };
                },
              ),
            };
          }
          return {};
        },
        triggerFields: ['authenticationType'],
      },
    },
    {
      component: 'Divider',
      fieldName: 'divider2',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-0',
      hideLabel: true,
      renderComponentContent() {
        return {
          default: () => '核销规则',
        };
      },
    },
    {
      component: markRaw(WriteOffT),
      fieldName: 'writeOffT',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '可核销时间',
      rules: 'required',
      defaultValue: [0, []],
      disabledOnChangeListener: false,
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'verificationMode',
      label: '核销验证方式',
      rules: 'selectRequired',
      componentProps: {
        options: accessAllEnums.value?.tickeVerificationMode.list,
      },
    },
    {
      component: 'RadioGroup',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      componentProps: {
        buttonStyle: 'solid',
        options: accessAllEnums.value?.ticketValidType.list.filter(
          (item: any) => item.value !== 4,
        ),
        optionType: 'default',
      },
      defaultValue: 1,
      fieldName: 'validType',
      label: '门票有效期',
    },
    {
      component: 'InputNumber',
      fieldName: 'validDayNum',
      label: '有效天数',
      componentProps: {
        placeholder: '请输入有效天数',
        allowClear: true,
        addonBefore: '',
        addonAfter: '天有效',
        precision: 0,
        min: 0,
      },
      dependencies: {
        show: ({ validType }) =>
          validType === 4 || validType === 2 || validType === 3,
        triggerFields: ['validType'],
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'validDate',
      label: '有效日期',
      componentProps: {
        format: 'YYYY-MM-DD',
        allowClear: true,
        placeholder: ['开始时间', '结束时间'],
        separator: '至',
        valueFormat: 'YYYY-MM-DD',
      },
      dependencies: {
        show: ({ validType }) => validType === 5,
        triggerFields: ['validType'],
      },
    },
    {
      component: 'CheckboxGroup',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      fieldName: 'applicablePeriod',
      label: '适用星期',
      rules: 'selectRequired',
      componentProps: {
        options: accessAllEnums.value?.ticketApplicablePeriod.list,
      },
      defaultValue: [0, 1, 2, 3, 4, 5, 6],
    },
    {
      component: 'Switch',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      fieldName: 'isUnavailableDate',
      label: '不可用日期',
      rules: 'required',
      defaultValue: 0,
      componentProps: {
        class: 'w-[45px]',
        checkedValue: 1,
        uncheckedValue: 0,
      },
    },
    {
      component: 'Select',
      fieldName: 'unavailableDateList',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '不可用日期',
      componentProps: {
        placeholder: '请选择不可用日期',
        allowClear: true,
        multiple: true,
        options: [],
      },
      dependencies: {
        show: ({ isUnavailableDate }) => isUnavailableDate === 1,
        triggerFields: ['isUnavailableDate'],
      },
    },
    {
      component: 'Switch',
      fieldName: 'isUnavailableHoliday',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '不可用节假日',
      rules: 'required',
      defaultValue: 0,
      componentProps: {
        class: 'w-[45px]',
        checkedValue: 1,
        uncheckedValue: 0,
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'unavailableHolidayList',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2',
      label: '不可用节假日',
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: any) => {
          return data.map((item: any) => ({
            label: item.holidayName,
            value: item.id,
          }));
        },
        // 菜单接口
        api: getHolidayAllList,
        placeholder: '请选择不可用节假日',
        allowClear: true,
        mode: 'multiple',
      },
      dependencies: {
        show: ({ isUnavailableHoliday }) => isUnavailableHoliday === 1,
        triggerFields: ['isUnavailableHoliday'],
      },
    },
    {
      component: 'Divider',
      fieldName: 'divider2',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-0',
      hideLabel: true,
      renderComponentContent() {
        return {
          default: () => '退票规则',
        };
      },
    },
    {
      component: 'Select',
      fieldName: 'refundType',
      label: '退票规则',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择退票规则',
        allowClear: true,
        options: accessAllEnums.value?.ticketRefundType.list.filter(
          (item: any) => item.value !== 5,
        ),
      },
      defaultValue: 1,
    },
    {
      component: 'InputNumber',
      fieldName: 'refundDay',
      label: '退票有效期',
      componentProps: {
        placeholder: '请输入天数',
        allowClear: true,
        addonBefore: '订单生成后',
        addonAfter: '天内可退',
        precision: 0,
        min: 0,
      },
      dependencies: {
        show: ({ refundType }) => refundType === 4,
        triggerFields: ['refundType'],
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '无需手续费', value: 0 },
          { label: '收取手续费', value: 1 },
        ],
        optionType: 'default',
      },
      dependencies: {
        show: ({ refundType }) => refundType != 1,
        triggerFields: ['refundType'],
      },
      defaultValue: 0,
      fieldName: 'isNeedRefundFee',
      label: '退票手续费',
      rules: 'required',
    },
    {
      component: markRaw(Fee),
      defaultValue: ['', 1],
      disabledOnChangeListener: false,
      fieldName: 'refundFee',
      label: '退款手续费',
      dependencies: {
        show: ({ isNeedRefundFee, refundType }) =>
          isNeedRefundFee === 1 && refundType != 1,
        triggerFields: ['isNeedRefundFee', 'refundType'],
      },
    },
    {
      component: 'Divider',
      fieldName: 'divider2',
      formItemClass: 'col-span-1 md:col-span-2 lg:col-span-2 pb-0',
      hideLabel: true,
      renderComponentContent() {
        return {
          default: () => '打印设置',
        };
      },
    },
    {
      component: 'Select',
      fieldName: 'windowPrintTemplate',
      label: '窗口打印模板',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择窗口打印模板',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'handPrintTemplate',
      label: '手持机打印模板',
      labelWidth: 150,
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择手持机打印模板',
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'selfDevicePrintTemplate',
      label: '自助机打印模板',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择自助机打印模板',
        allowClear: true,
      },
    },
  ];
}
