<script setup lang="ts">
import type { Recordable } from '@vben/types';
import { computed, ref, toRefs, watch, defineProps, defineEmits } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Input, Tag, Button, Table, Form, FormItem } from 'ant-design-vue';
import { getAdminUserList } from '#/api';
import { useAccessStore } from '@vben/stores';
const { accessAllEnums } = toRefs(useAccessStore());

const emits = defineEmits(['change']);
const props = defineProps({
  selectedUsers: {
    type: Array,
    default: () => [],
  },
});

const [Model, modelApi] = useVbenModal({
  title: '用户列表',
  async onConfirm() {
    await handleSubmit();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      setTimeout(() => {
        getUserLists();
      }, 0);
    }
  },
});
const columns = ref<any>([
  {
    title: '账户名',
    dataIndex: 'userName',
    align: 'center',
  },
  {
    title: '姓名',
    dataIndex: 'name',
    align: 'center',
  },
  {
    title: '性别',
    dataIndex: 'sex',
    align: 'center',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    align: 'center',
  },
  {
    title: '状态',
    dataIndex: 'status',
    align: 'center',
  },
]);
const params = ref({
  name: undefined,
  page: 1,
  pageSize: 10,
});

const userList = ref<any[]>([]);
const total = ref(0);
const getUserLists = async () => {
  const res = await getAdminUserList(params.value);
  userList.value = res.list;
  total.value = res.total;
};
const resetForm = () => {
  params.value = {
    name: undefined,
    page: 1,
    pageSize: 10,
  };
  getUserLists();
};
const handleSearch = () => {
  params.value.page = 1;
  getUserLists();
};


const selectedRowKeys = ref<any[]>([]);
const selectedRows = ref<any[]>([]);

watch(
  () => props.selectedUsers,
  (val) => {
    if (val && val.length > 0) {
      console.log(val, 'selectedUsers');
      selectedRows.value = val;
      selectedRowKeys.value = val.map((item: any) => item.id);
    } else {
      selectedRows.value = [];
      selectedRowKeys.value = [];
    }
  },
  { immediate: true, deep: true },
);

const rowSelection = computed(() => ({
  checkStrictly: false,
  selectedRowKeys: selectedRowKeys.value.filter(key => userList.value.some(item => item.id === key)), // 仅显示当前页面的选定按键
  onChange: (
    currentSelectedKeys: (string | number)[],
    currentSelectedRows: any[],
  ) => {
    const newGlobalSelectedKeys = new Set(selectedRowKeys.value);
    const newGlobalSelectedRows = [...selectedRows.value];

    const currentPageRowIds = new Set(userList.value.map(item => item.id));

    // 1. Handle deselections:
    const deselectedOnCurrentPage = Array.from(newGlobalSelectedKeys).filter(key =>
      currentPageRowIds.has(key) && !currentSelectedKeys.includes(key)
    );
    deselectedOnCurrentPage.forEach(key => {
      newGlobalSelectedKeys.delete(key);
      const index = newGlobalSelectedRows.findIndex(row => row.id === key);
      if (index !== -1) {
        newGlobalSelectedRows.splice(index, 1);
      }
    });

    // 2. Handle selections:
    currentSelectedRows.forEach(row => {
      if (!newGlobalSelectedKeys.has(row.id)) {
        newGlobalSelectedKeys.add(row.id);
        newGlobalSelectedRows.push(row);
      }
    });

    selectedRowKeys.value = Array.from(newGlobalSelectedKeys);
    selectedRows.value = newGlobalSelectedRows;
  },
  onSelect: (record: any, selected: boolean) => {
    if (selected) {
      if (!selectedRows.value.some((item) => item.id === record.id)) {
        selectedRows.value.push(record);
        selectedRowKeys.value.push(record.id);
      }
    } else {
      selectedRows.value = selectedRows.value.filter(
        (item) => item.id !== record.id,
      );
      selectedRowKeys.value = selectedRowKeys.value.filter(
        (key) => key !== record.id,
      );
    }
  },
  onSelectAll: (
    selected: boolean,
    currentSelectedRows: any[],
    changeRows: any[],
  ) => {
    if (selected) {
      changeRows.forEach((row) => {
        if (!selectedRows.value.some((item) => item.id === row.id)) {
          selectedRows.value.push(row);
          selectedRowKeys.value.push(row.id);
        }
      });
    } else {
      const changeRowIds = new Set(changeRows.map((row) => row.id));
      selectedRows.value = selectedRows.value.filter(
        (item) => !changeRowIds.has(item.id),
      );
      selectedRowKeys.value = selectedRowKeys.value.filter(
        (key) => !changeRowIds.has(key),
      );
    }
  },
}));

const handleSubmit = () => {
  emits(
    'change',
    selectedRows.value
  );
  modelApi.close();
};

const filterStatus = (val: any) => {
  const statusMap = [
    { color: 'success', label: '启用', value: 1 },
    { color: 'error', label: '禁用', value: -1 },
    { color: 'warning', label: '锁定', value: -2 },
  ]
  return statusMap.find((item: any) => item.value === val);
};
</script>
<template>
  <Model class="w-[50%]" title="用户列表">
    <Form layout="inline" :model="params" class="mb-2">
      <div class="grid grid-cols-3 gap-2">
        <FormItem label="姓名">
          <Input v-model:value="params.name" allowClear placeholder="请输入姓名" />
        </FormItem>
        <FormItem>
          <Button @click="resetForm()">重置</Button>
          <Button type="primary" class="ml-2" @click="handleSearch()">搜索</Button>
        </FormItem>
      </div>
    </Form>
    <div>
      <Table :rowSelection="rowSelection" :columns="columns" :dataSource="userList" rowKey="id" :pagination="{
        current: params.page,
        pageSize: params.pageSize,
        total: total,
        onChange: (page, pageSize) => {
          params.page = page;
          params.pageSize = pageSize;
          getUserLists();
        },
        showTotal: (total) => `共 ${total} 条`,
      }" bordered>
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'sex'">
            {{ record.sex === 1 ? '男' : record.sex === 2 ? '女' : '保密' }}
          </template>
          <template v-if="column.dataIndex === 'status'">
            <Tag :color="filterStatus(record.status)?.color">{{ filterStatus(record.status)?.label }}</Tag>
          </template>
        </template>
      </Table>
    </div>
  </Model>
</template>
