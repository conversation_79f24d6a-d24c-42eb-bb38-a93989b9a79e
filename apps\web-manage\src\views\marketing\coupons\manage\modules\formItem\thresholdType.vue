<script lang="ts" setup>
import { RadioGroup, InputNumber } from 'ant-design-vue';

const emit = defineEmits(['blur', 'change']);
const modelValue = defineModel<[number, number]>({
    default: () => [1, 0],
});

function onChange() {
    emit('change', modelValue.value);
}
</script>
<template>
    <div class="flex h-[32px] w-full items-center gap-1">
        <div class="w-2/5">
            <RadioGroup v-model:value="modelValue[0]" :options="[
                { label: '无门槛', value: 1 },
                { label: '最低消费', value: 2 },
            ]" @change="onChange" @blur="emit('blur', modelValue)" class="w-full" />
        </div>
        <div class="flex flex-1 items-center" v-if="modelValue[0] === 2">
            <div class="mr-2 w-[120px] text-right text-sm font-[500] leading-6">
                最低消费金额
            </div>
            <InputNumber v-model:value="modelValue[1]" placeholder="请输入最低消费金额" allow-clear @change="onChange"
                @blur="emit('blur', modelValue)" class="w-full" addon-after="元" />
        </div>
    </div>
</template>
