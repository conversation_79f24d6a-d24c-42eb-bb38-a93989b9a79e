<script lang="ts" setup>
import { computed } from 'vue';

import { AuthPageLayout } from '@vben/layouts';
import { preferences } from '@vben/preferences';


const appName = computed(() => preferences.app.name);
const logo = computed(() => preferences.logo.source);
</script>

<template>
  <AuthPageLayout
    :app-name="appName"
    :logo="logo"
    :toolbar-list="['color', 'layout', 'theme']"
    :toolbar="true"
    slogan-image="https://educdn.xjzredu.cn/ticket/login_img.gif"
    :copyright="true"
    page-description="智慧票务，让景区管理更高效，游客体验更轻松"
    page-title="高效管理，畅快游玩"
  >
    <!-- 自定义工具栏 -->
    <!-- <template #toolbar></template> -->
  </AuthPageLayout>
</template>
