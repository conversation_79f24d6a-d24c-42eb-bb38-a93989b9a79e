<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
const videoUrl = ref('');

const [videoModel, videoModelApi] = useVbenModal({
  title: '视频播放',
  footer: false,
  async onConfirm() {
    videoModelApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      videoUrl.value = videoModelApi.getData<any>().videoUrl;
    }
  },
});
</script>
<template>
  <videoModel>
    <div class="h-full w-full">
      <video :src="videoUrl" controls class="max-h-[500px]" />
    </div>
  </videoModel>
</template>
